"""
Mock Exchange for Testing
Simulates exchange behavior without real trading
"""

import time
import random
from datetime import datetime
from typing import Dict, List, Optional

class MockExchange:
    """
    Mock exchange that simulates real exchange behavior for testing
    """
    
    def __init__(self, initial_balance: float = 10000.0):
        self.balance = {
            'USDT': {'free': initial_balance, 'used': 0.0, 'total': initial_balance},
            'BTC': {'free': 0.0, 'used': 0.0, 'total': 0.0},
            'ETH': {'free': 0.0, 'used': 0.0, 'total': 0.0}
        }
        self.orders = []
        self.positions = {}
        self.order_id_counter = 1
        self.slippage = 0.001  # 0.1% slippage
        self.fee_rate = 0.001  # 0.1% fee
        
        # Market data simulation
        self.prices = {
            'BTC/USDT': 50000.0,
            'ETH/USDT': 3000.0,
            'ADA/USDT': 0.5,
            'DOGE/USDT': 0.1
        }
        
        # Simulate price movements
        self._update_prices()
    
    def _update_prices(self):
        """Simulate realistic price movements"""
        for symbol in self.prices:
            # Random walk with slight upward bias
            change = random.gauss(0.0001, 0.005)  # 0.01% mean, 0.5% std
            self.prices[symbol] *= (1 + change)
            self.prices[symbol] = max(0.001, self.prices[symbol])  # Prevent negative prices
    
    def create_order(self, symbol: str, type: str, side: str, amount: float, 
                    price: Optional[float] = None, **kwargs) -> Dict:
        """Create a mock order"""
        self._update_prices()
        
        order_id = f"MOCK_{self.order_id_counter}"
        self.order_id_counter += 1
        
        # Get current market price
        market_price = self.prices.get(symbol, 50000.0)
        
        # Apply slippage for market orders
        if type == 'market':
            if side == 'buy':
                execution_price = market_price * (1 + self.slippage)
            else:
                execution_price = market_price * (1 - self.slippage)
        else:
            execution_price = price or market_price
        
        # Calculate fee
        fee_amount = amount * execution_price * self.fee_rate
        
        # Simulate order execution
        order = {
            'id': order_id,
            'symbol': symbol,
            'type': type,
            'side': side,
            'amount': amount,
            'price': execution_price,
            'filled': amount,
            'remaining': 0.0,
            'average': execution_price,
            'status': 'closed',
            'timestamp': int(datetime.now().timestamp() * 1000),
            'fee': {
                'cost': fee_amount,
                'currency': 'USDT'
            },
            'info': {
                'leverage': kwargs.get('leverage', 1.0)
            }
        }
        
        # Update balance (simplified)
        if side == 'buy':
            cost = amount * execution_price + fee_amount
            if self.balance['USDT']['free'] >= cost:
                self.balance['USDT']['free'] -= cost
                base_currency = symbol.split('/')[0]
                if base_currency not in self.balance:
                    self.balance[base_currency] = {'free': 0.0, 'used': 0.0, 'total': 0.0}
                self.balance[base_currency]['free'] += amount
            else:
                order['status'] = 'rejected'
                order['filled'] = 0.0
        else:  # sell
            base_currency = symbol.split('/')[0]
            if base_currency in self.balance and self.balance[base_currency]['free'] >= amount:
                self.balance[base_currency]['free'] -= amount
                proceeds = amount * execution_price - fee_amount
                self.balance['USDT']['free'] += proceeds
            else:
                order['status'] = 'rejected'
                order['filled'] = 0.0
        
        self.orders.append(order)
        return order
    
    def fetch_balance(self) -> Dict:
        """Fetch account balance"""
        return self.balance.copy()
    
    def fetch_ticker(self, symbol: str) -> Dict:
        """Fetch ticker information"""
        self._update_prices()
        price = self.prices.get(symbol, 50000.0)
        
        return {
            'symbol': symbol,
            'bid': price * 0.9995,
            'ask': price * 1.0005,
            'last': price,
            'open': price * 0.99,
            'high': price * 1.02,
            'low': price * 0.98,
            'close': price,
            'baseVolume': random.uniform(100, 1000),
            'quoteVolume': random.uniform(1000000, ********),
            'timestamp': int(datetime.now().timestamp() * 1000)
        }
    
    def fetch_order_book(self, symbol: str, limit: int = 20) -> Dict:
        """Fetch order book"""
        self._update_prices()
        price = self.prices.get(symbol, 50000.0)
        
        bids = []
        asks = []
        
        for i in range(limit):
            bid_price = price * (1 - (i + 1) * 0.0001)
            ask_price = price * (1 + (i + 1) * 0.0001)
            bid_amount = random.uniform(0.1, 10.0)
            ask_amount = random.uniform(0.1, 10.0)
            
            bids.append([bid_price, bid_amount])
            asks.append([ask_price, ask_amount])
        
        return {
            'bids': bids,
            'asks': asks,
            'timestamp': int(datetime.now().timestamp() * 1000),
            'datetime': datetime.now().isoformat(),
            'nonce': None
        }
    
    def fetch_ohlcv(self, symbol: str, timeframe: str = '1m', 
                   since: Optional[int] = None, limit: int = 100) -> List[List]:
        """Fetch OHLCV data"""
        self._update_prices()
        current_price = self.prices.get(symbol, 50000.0)
        
        ohlcv = []
        timestamp = int(datetime.now().timestamp() * 1000)
        
        for i in range(limit):
            # Generate realistic OHLCV data
            open_price = current_price * (1 + random.gauss(0, 0.01))
            high_price = open_price * (1 + abs(random.gauss(0, 0.005)))
            low_price = open_price * (1 - abs(random.gauss(0, 0.005)))
            close_price = open_price * (1 + random.gauss(0, 0.005))
            volume = random.uniform(10, 1000)
            
            ohlcv.append([
                timestamp - (i * 60000),  # 1 minute intervals
                open_price,
                high_price,
                low_price,
                close_price,
                volume
            ])
        
        return list(reversed(ohlcv))  # Return chronological order
    
    def fetch_order(self, order_id: str, symbol: str = None) -> Dict:
        """Fetch order by ID"""
        for order in self.orders:
            if order['id'] == order_id:
                return order
        
        raise Exception(f"Order {order_id} not found")
    
    def cancel_order(self, order_id: str, symbol: str = None) -> Dict:
        """Cancel an order"""
        for order in self.orders:
            if order['id'] == order_id and order['status'] == 'open':
                order['status'] = 'canceled'
                return order
        
        raise Exception(f"Order {order_id} not found or cannot be canceled")
    
    def fetch_positions(self, symbols: List[str] = None) -> List[Dict]:
        """Fetch open positions"""
        return list(self.positions.values())
    
    def set_leverage(self, leverage: float, symbol: str) -> Dict:
        """Set leverage for symbol"""
        return {
            'symbol': symbol,
            'leverage': leverage,
            'info': {}
        }
    
    def fetch_trading_fees(self, symbol: str = None) -> Dict:
        """Fetch trading fees"""
        return {
            'maker': self.fee_rate,
            'taker': self.fee_rate,
            'percentage': True,
            'tierBased': False
        }
    
    def get_market_price(self, symbol: str) -> float:
        """Get current market price"""
        self._update_prices()
        return self.prices.get(symbol, 50000.0)
    
    def simulate_market_movement(self, symbol: str, direction: str, magnitude: float = 0.01):
        """Simulate specific market movement for testing"""
        if symbol in self.prices:
            if direction == 'up':
                self.prices[symbol] *= (1 + magnitude)
            elif direction == 'down':
                self.prices[symbol] *= (1 - magnitude)
    
    def reset_balance(self, initial_balance: float = 10000.0):
        """Reset balance for testing"""
        self.balance = {
            'USDT': {'free': initial_balance, 'used': 0.0, 'total': initial_balance},
            'BTC': {'free': 0.0, 'used': 0.0, 'total': 0.0},
            'ETH': {'free': 0.0, 'used': 0.0, 'total': 0.0}
        }
        self.orders.clear()
        self.positions.clear()
    
    def get_order_history(self) -> List[Dict]:
        """Get order history for analysis"""
        return self.orders.copy()
