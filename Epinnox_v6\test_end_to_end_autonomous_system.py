#!/usr/bin/env python3
"""
End-to-End Autonomous System Testing
Complete autonomous trading cycles with live data - full pipeline testing
"""

import sys
import os
import asyncio
import json
import time
from datetime import datetime, timedelta
import logging

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from trading.ccxt_trading_engine import CCXTTradingEngine
from portfolio.portfolio_manager import PortfolioManager
from execution.autonomous_executor import AutonomousTradeExecutor
from tests.mocks.mock_rl_agent import MockRLAgent
from tests.mocks.mock_trading_environment import MockTradingEnvironment

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EndToEndAutonomousSystemTester:
    """Test complete autonomous trading system end-to-end"""
    
    def __init__(self):
        self.results = []
        self.exchange_engine = None
        self.portfolio_manager = None
        self.autonomous_executor = None
        self.rl_agent = None
        self.trading_env = None
        self.test_symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT']
    
    async def setup_autonomous_system(self, initial_balance=10000.0):
        """Setup complete autonomous trading system"""
        logger.info("🔗 Setting up end-to-end autonomous trading system...")
        
        # Setup exchange connection with live data
        self.exchange_engine = CCXTTradingEngine('htx', demo_mode=True)
        if not self.exchange_engine.initialize_exchange():
            raise Exception("Failed to connect to exchange for autonomous testing")
        
        # Setup portfolio manager with conservative settings
        self.portfolio_manager = PortfolioManager(initial_balance=initial_balance, max_positions=3)
        self.portfolio_manager.set_risk_limits(
            max_portfolio_risk=0.10,  # 10% max portfolio risk
            max_position_size=0.05,   # 5% max position size
            max_leverage=1.5,         # 1.5x max leverage
            max_daily_loss=0.03       # 3% max daily loss
        )
        
        # Setup autonomous executor
        self.autonomous_executor = AutonomousTradeExecutor(self.exchange_engine.exchange)
        
        # Setup AI components
        self.rl_agent = MockRLAgent()
        self.trading_env = MockTradingEnvironment(initial_balance=initial_balance)
        
        logger.info("✅ End-to-end autonomous trading system ready")
    
    def mock_llm_decision(self, symbol, market_data):
        """Mock LLM decision making for autonomous system"""
        import random
        
        # Simulate LLM processing
        time.sleep(random.uniform(0.5, 1.5))
        
        price_change = market_data.get('price_change_24h', 0.0)
        volatility = market_data.get('volatility', 0.02)
        
        # Generate decision based on market conditions
        if abs(price_change) < 0.01 and volatility < 0.02:
            decision = 'WAIT'
            confidence = 0.6 + random.uniform(0, 0.2)
        elif price_change > 0.02:
            decision = 'LONG'
            confidence = 0.7 + random.uniform(0, 0.2)
        elif price_change < -0.02:
            decision = 'SHORT'
            confidence = 0.7 + random.uniform(0, 0.2)
        else:
            decision = random.choice(['LONG', 'SHORT', 'WAIT'])
            confidence = 0.5 + random.uniform(0, 0.3)
        
        return {
            'decision': decision,
            'confidence': min(0.95, max(0.1, confidence)),
            'reasoning': f"LLM analysis for {symbol}: {decision} based on price change {price_change:.2%} and volatility {volatility:.2%}"
        }
    
    async def test_complete_trading_cycle(self):
        """Test complete autonomous trading cycle"""
        logger.info("🔧 Testing Complete Autonomous Trading Cycle")
        
        cycle_results = {}
        
        for symbol in self.test_symbols:
            try:
                logger.info(f"Running complete trading cycle for {symbol}...")
                
                # Step 1: Data Ingestion
                start_time = time.time()
                ticker = self.exchange_engine.exchange.fetch_ticker(symbol)
                ohlcv = self.exchange_engine.exchange.fetch_ohlcv(symbol, '1m', limit=100)
                orderbook = self.exchange_engine.exchange.fetch_order_book(symbol, limit=10)
                data_ingestion_time = time.time() - start_time
                
                # Step 2: Market Data Processing
                start_time = time.time()
                current_price = ticker['last']
                price_change_24h = ticker['percentage'] / 100 if ticker['percentage'] else 0.0
                volume_24h = ticker['quoteVolume'] if ticker['quoteVolume'] else 1000000
                
                # Calculate volatility
                if len(ohlcv) >= 20:
                    prices = [candle[4] for candle in ohlcv[-20:]]
                    returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
                    volatility = (sum(r**2 for r in returns) / len(returns)) ** 0.5
                else:
                    volatility = 0.02
                
                market_data = {
                    'current_price': current_price,
                    'price_change_24h': price_change_24h,
                    'volume_24h': volume_24h,
                    'volatility': volatility,
                    'spread': orderbook['asks'][0][0] - orderbook['bids'][0][0] if orderbook['bids'] and orderbook['asks'] else 0
                }
                data_processing_time = time.time() - start_time
                
                # Step 3: AI Analysis (RL Agent)
                start_time = time.time()
                rl_state = {
                    'price': current_price,
                    'volatility': volatility,
                    'trend': price_change_24h,
                    'volume_ratio': volume_24h / 1000000
                }
                rl_action = self.rl_agent.get_action(rl_state)
                ai_analysis_time = time.time() - start_time
                
                # Step 4: LLM Decision Making
                start_time = time.time()
                llm_decision = self.mock_llm_decision(symbol, market_data)
                llm_decision_time = time.time() - start_time
                
                # Step 5: Decision Aggregation
                start_time = time.time()
                # Simple aggregation logic
                if llm_decision['decision'] == rl_action and llm_decision['confidence'] > 0.6:
                    final_decision = llm_decision['decision']
                    final_confidence = llm_decision['confidence']
                elif llm_decision['confidence'] > 0.8:
                    final_decision = llm_decision['decision']
                    final_confidence = llm_decision['confidence']
                else:
                    final_decision = 'WAIT'
                    final_confidence = 0.5
                decision_aggregation_time = time.time() - start_time
                
                # Step 6: Risk Assessment
                start_time = time.time()
                position_size = self.portfolio_manager.current_balance * 0.04  # 4% position
                can_trade = await self.portfolio_manager.can_open_position(symbol, position_size, 1.0)
                risk_assessment_time = time.time() - start_time
                
                # Step 7: Trade Execution (Simulated)
                start_time = time.time()
                trade_executed = False
                if final_decision != 'WAIT' and can_trade['allowed'] and final_confidence > 0.6:
                    try:
                        # Simulate trade execution
                        await self.portfolio_manager.open_position(
                            symbol, 
                            'long' if final_decision == 'LONG' else 'short',
                            position_size / current_price,  # Convert to quantity
                            current_price,
                            1.0
                        )
                        trade_executed = True
                        logger.info(f"✅ {symbol}: {final_decision} position opened")
                    except Exception as e:
                        logger.warning(f"Trade execution failed for {symbol}: {e}")
                trade_execution_time = time.time() - start_time
                
                # Calculate total cycle time
                total_cycle_time = (data_ingestion_time + data_processing_time + 
                                  ai_analysis_time + llm_decision_time + 
                                  decision_aggregation_time + risk_assessment_time + 
                                  trade_execution_time)
                
                cycle_results[symbol] = {
                    'data_ingestion_time': data_ingestion_time,
                    'data_processing_time': data_processing_time,
                    'ai_analysis_time': ai_analysis_time,
                    'llm_decision_time': llm_decision_time,
                    'decision_aggregation_time': decision_aggregation_time,
                    'risk_assessment_time': risk_assessment_time,
                    'trade_execution_time': trade_execution_time,
                    'total_cycle_time': total_cycle_time,
                    'market_data': market_data,
                    'rl_action': rl_action,
                    'llm_decision': llm_decision,
                    'final_decision': final_decision,
                    'final_confidence': final_confidence,
                    'trade_executed': trade_executed,
                    'can_trade': can_trade,
                    'success': total_cycle_time < 10.0 and final_decision in ['LONG', 'SHORT', 'WAIT']
                }
                
                logger.info(f"✅ {symbol}: Complete cycle in {total_cycle_time:.2f}s, decision: {final_decision}")
                
            except Exception as e:
                logger.error(f"❌ Complete trading cycle failed for {symbol}: {e}")
                cycle_results[symbol] = {'error': str(e), 'success': False}
        
        successful_cycles = sum(1 for r in cycle_results.values() if r.get('success', False))
        
        result = {
            'test': 'Complete Autonomous Trading Cycle',
            'symbols_tested': len(self.test_symbols),
            'successful_cycles': successful_cycles,
            'cycle_results': cycle_results,
            'success': successful_cycles >= 1  # At least 1 symbol should complete successfully
        }
        
        self.results.append(result)
        return result['success']
    
    async def test_multi_symbol_coordination(self):
        """Test autonomous system coordination across multiple symbols"""
        logger.info("🔧 Testing Multi-Symbol Coordination")
        
        try:
            # Get market data for all symbols simultaneously
            market_data = {}
            for symbol in self.test_symbols:
                ticker = self.exchange_engine.exchange.fetch_ticker(symbol)
                market_data[symbol] = {
                    'price': ticker['last'],
                    'change_24h': ticker['percentage'] / 100 if ticker['percentage'] else 0.0,
                    'volume': ticker['quoteVolume'] if ticker['quoteVolume'] else 1000000
                }
            
            # Test portfolio-level decision making
            total_portfolio_risk = self.portfolio_manager.calculate_total_portfolio_risk()
            available_risk_budget = self.portfolio_manager.max_portfolio_risk - total_portfolio_risk
            
            # Simulate coordinated decision making
            symbol_priorities = []
            for symbol, data in market_data.items():
                volatility = abs(data['change_24h'])
                opportunity_score = volatility * (data['volume'] / 1000000)
                symbol_priorities.append((symbol, opportunity_score))
            
            # Sort by opportunity score
            symbol_priorities.sort(key=lambda x: x[1], reverse=True)
            
            # Test coordinated execution
            coordinated_decisions = {}
            risk_allocated = 0.0
            
            for symbol, opportunity_score in symbol_priorities:
                if risk_allocated < available_risk_budget:
                    # Allocate risk based on opportunity and available budget
                    risk_allocation = min(0.03, available_risk_budget - risk_allocated)  # Max 3% per position
                    
                    coordinated_decisions[symbol] = {
                        'opportunity_score': opportunity_score,
                        'risk_allocation': risk_allocation,
                        'priority_rank': len(coordinated_decisions) + 1,
                        'decision': 'LONG' if market_data[symbol]['change_24h'] > 0 else 'SHORT'
                    }
                    
                    risk_allocated += risk_allocation
                else:
                    coordinated_decisions[symbol] = {
                        'opportunity_score': opportunity_score,
                        'risk_allocation': 0.0,
                        'priority_rank': len(coordinated_decisions) + 1,
                        'decision': 'WAIT'
                    }
            
            # Validate coordination logic
            total_risk_allocated = sum(d['risk_allocation'] for d in coordinated_decisions.values())
            coordination_valid = total_risk_allocated <= available_risk_budget
            
            result = {
                'test': 'Multi-Symbol Coordination',
                'symbols_coordinated': len(self.test_symbols),
                'market_data': market_data,
                'coordinated_decisions': coordinated_decisions,
                'total_risk_allocated': total_risk_allocated,
                'available_risk_budget': available_risk_budget,
                'coordination_valid': coordination_valid,
                'success': coordination_valid and len(coordinated_decisions) == len(self.test_symbols)
            }
            
            self.results.append(result)
            logger.info(f"✅ Multi-symbol coordination: {total_risk_allocated:.1%} risk allocated")
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Multi-symbol coordination test failed: {e}")
            result = {
                'test': 'Multi-Symbol Coordination',
                'error': str(e),
                'success': False
            }
            self.results.append(result)
            return False
    
    async def test_system_resilience(self):
        """Test autonomous system resilience under stress"""
        logger.info("🔧 Testing System Resilience")
        
        resilience_tests = []
        
        # Test 1: Rapid market data updates
        try:
            rapid_updates = []
            for i in range(10):
                start_time = time.time()
                ticker = self.exchange_engine.exchange.fetch_ticker('BTC/USDT:USDT')
                update_time = time.time() - start_time
                rapid_updates.append(update_time)
                await asyncio.sleep(0.1)  # 100ms intervals
            
            avg_update_time = sum(rapid_updates) / len(rapid_updates)
            rapid_updates_success = avg_update_time < 2.0  # Under 2 seconds average
            
            resilience_tests.append({
                'test_name': 'Rapid Market Data Updates',
                'avg_update_time': avg_update_time,
                'success': rapid_updates_success
            })
            
        except Exception as e:
            resilience_tests.append({
                'test_name': 'Rapid Market Data Updates',
                'error': str(e),
                'success': False
            })
        
        # Test 2: Concurrent symbol processing
        try:
            async def process_symbol(symbol):
                ticker = self.exchange_engine.exchange.fetch_ticker(symbol)
                return {'symbol': symbol, 'price': ticker['last']}
            
            start_time = time.time()
            tasks = [process_symbol(symbol) for symbol in self.test_symbols]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            concurrent_time = time.time() - start_time
            
            successful_results = [r for r in results if not isinstance(r, Exception)]
            concurrent_success = len(successful_results) == len(self.test_symbols) and concurrent_time < 5.0
            
            resilience_tests.append({
                'test_name': 'Concurrent Symbol Processing',
                'concurrent_time': concurrent_time,
                'successful_symbols': len(successful_results),
                'success': concurrent_success
            })
            
        except Exception as e:
            resilience_tests.append({
                'test_name': 'Concurrent Symbol Processing',
                'error': str(e),
                'success': False
            })
        
        # Test 3: Error recovery
        try:
            # Simulate error condition and recovery
            original_balance = self.portfolio_manager.current_balance
            
            # Test portfolio state recovery
            portfolio_summary = self.portfolio_manager.get_portfolio_summary()
            recovery_success = (
                portfolio_summary['current_balance'] == original_balance and
                'total_value' in portfolio_summary and
                'unrealized_pnl' in portfolio_summary
            )
            
            resilience_tests.append({
                'test_name': 'Error Recovery',
                'portfolio_state_valid': recovery_success,
                'success': recovery_success
            })
            
        except Exception as e:
            resilience_tests.append({
                'test_name': 'Error Recovery',
                'error': str(e),
                'success': False
            })
        
        successful_resilience_tests = sum(1 for t in resilience_tests if t.get('success', False))
        
        result = {
            'test': 'System Resilience',
            'resilience_tests': resilience_tests,
            'successful_tests': successful_resilience_tests,
            'total_tests': len(resilience_tests),
            'success': successful_resilience_tests >= 2  # At least 2 out of 3 tests should pass
        }
        
        self.results.append(result)
        logger.info(f"✅ System resilience: {successful_resilience_tests}/{len(resilience_tests)} tests passed")
        return result['success']
    
    def generate_autonomous_system_report(self):
        """Generate comprehensive autonomous system testing report"""
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'total_tests': len(self.results),
            'passed_tests': sum(1 for r in self.results if r['success']),
            'failed_tests': sum(1 for r in self.results if not r['success']),
            'success_rate': sum(1 for r in self.results if r['success']) / len(self.results) * 100 if self.results else 0,
            'autonomous_system_tests': self.results
        }
        
        # Save report
        filename = f"end_to_end_autonomous_system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        return report, filename

async def main():
    """Run all end-to-end autonomous system tests"""
    print("🚀 Starting End-to-End Autonomous System Testing")
    print("=" * 60)
    
    tester = EndToEndAutonomousSystemTester()
    
    # Setup autonomous system
    try:
        await tester.setup_autonomous_system()
    except Exception as e:
        print(f"❌ Failed to setup autonomous system: {e}")
        return False
    
    # Run all autonomous system tests
    tests = [
        tester.test_complete_trading_cycle,
        tester.test_multi_symbol_coordination,
        tester.test_system_resilience,
    ]
    
    results = []
    for i, test in enumerate(tests, 1):
        print(f"\n🔧 Running autonomous system test {i}/{len(tests)}...")
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Autonomous system test {i} failed with exception: {e}")
            results.append(False)
    
    # Generate report
    report, filename = tester.generate_autonomous_system_report()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 END-TO-END AUTONOMOUS SYSTEM TESTING SUMMARY")
    print("=" * 60)
    
    for i, (test_result, success) in enumerate(zip(tester.results, results), 1):
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - Test {i}: {test_result['test']}")
    
    print(f"\nResults: {report['passed_tests']}/{report['total_tests']} tests passed")
    print(f"Success Rate: {report['success_rate']:.1f}%")
    print(f"📄 Detailed report saved to: {filename}")
    
    if report['success_rate'] >= 75:
        print("🎉 End-to-end autonomous system testing PASSED!")
        return True
    else:
        print("⚠️ Some autonomous system tests failed. Review the detailed report.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
