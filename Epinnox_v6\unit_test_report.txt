============================= test session starts =============================
platform win32 -- Python 3.9.13, pytest-8.4.1, pluggy-1.5.0 -- C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe
cachedir: .pytest_cache
metadata: {'Python': '3.9.13', 'Platform': 'Windows-10-10.0.26100-SP0', 'Packages': {'pytest': '8.4.1', 'pluggy': '1.5.0'}, 'Plugins': {'anyio': '4.9.0', 'dash': '3.0.4', 'asyncio': '0.26.0', 'cov': '6.2.1', 'html': '4.1.1', 'metadata': '3.1.1', 'mock': '3.14.1'}}
rootdir: C:\Users\<USER>\Documents\dev\Epinnox_v6
configfile: pytest.ini
plugins: anyio-4.9.0, dash-3.0.4, asyncio-0.26.0, cov-6.2.1, html-4.1.1, metadata-3.1.1, mock-3.14.1
asyncio: mode=strict, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function
collecting ... collected 42 items

tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_trading_decision_high_confidence PASSED [  2%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_trading_decision_low_confidence PASSED [  4%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_trading_decision_wait PASSED [  7%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_pre_execution_risk_check_excessive_leverage PASSED [  9%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_pre_execution_risk_check_large_position PASSED [ 11%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_order_success PASSED [ 14%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_order_with_futures_symbol PASSED [ 16%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_get_account_balance PASSED [ 19%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_setup_risk_orders PASSED [ 21%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_trade_order_creation PASSED [ 23%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_missing_position_sizing_data PASSED [ 26%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_init_database PASSED [ 28%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_record_trade PASSED [ 30%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_daily_metrics_empty PASSED [ 33%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_daily_metrics_with_trades PASSED [ 35%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_sharpe_ratio PASSED [ 38%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_sharpe_ratio_empty PASSED [ 40%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_max_drawdown PASSED [ 42%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_profit_factor PASSED [ 45%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_profit_factor_no_losses PASSED [ 47%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_get_model_performance_empty PASSED [ 50%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_get_model_performance_with_data PASSED [ 52%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary PASSED [ 54%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_empty PASSED [ 57%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_save_daily_metrics PASSED [ 59%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_can_open_position_success PASSED [ 61%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_can_open_position_max_positions PASSED [ 64%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_can_open_position_excessive_size PASSED [ 66%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_can_open_position_excessive_leverage PASSED [ 69%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_open_position_success 