"""
Autonomous Trade Executor
Core component that executes trades autonomously based on AI decisions
"""

import asyncio
import time
import logging
from typing import Dict, Optional, List
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"

@dataclass
class TradeOrder:
    symbol: str
    side: str  # 'buy' or 'sell'
    amount: float
    price: Optional[float] = None
    order_type: OrderType = OrderType.MARKET
    leverage: float = 1.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None

class AutonomousTradeExecutor:
    """
    Autonomous trade executor that makes real trading decisions and executes them
    """
    
    def __init__(self, exchange, risk_manager=None, min_confidence=0.7):
        self.exchange = exchange
        self.risk_manager = risk_manager
        self.min_confidence = min_confidence
        self.active_orders = {}
        self.position_tracker = {}
        
    async def execute_trading_decision(self, decision_data: dict) -> dict:
        """
        Execute trading decision autonomously
        
        Args:
            decision_data: Dict containing:
                - decision: 'LONG', 'SHORT', 'WAIT'
                - confidence: 0-100
                - symbol: trading symbol
                - leverage_position_sizing: position sizing data
                
        Returns:
            Dict with execution results
        """
        try:
            decision = decision_data.get('decision', 'WAIT')
            confidence = decision_data.get('confidence', 0) / 100
            symbol = decision_data.get('selected_symbol', 'DOGE/USDT')
            
            # AUTONOMOUS DECISION: Only trade if confidence is high enough
            if confidence < self.min_confidence:
                return {
                    'status': 'SKIPPED',
                    'reason': f'Confidence {confidence:.1%} below threshold {self.min_confidence:.1%}',
                    'action': 'WAIT'
                }
                
            if decision == 'WAIT':
                return {'status': 'WAIT', 'reason': 'System decided to wait'}
                
            # Get position sizing data
            position_data = decision_data.get('leverage_position_sizing', {})
            if not position_data:
                return {'status': 'ERROR', 'reason': 'No position sizing data'}
                
            # Pre-execution risk checks
            risk_check = await self.pre_execution_risk_check(symbol, position_data, confidence)
            if not risk_check['approved']:
                return {
                    'status': 'RISK_REJECTED',
                    'reason': risk_check['reason'],
                    'risk_warnings': risk_check.get('warnings', [])
                }
                
            # Create trade order
            trade_order = TradeOrder(
                symbol=symbol,
                side='buy' if decision == 'LONG' else 'sell',
                amount=position_data.get('position_units', 0),
                leverage=position_data.get('effective_leverage', 1.0),
                stop_loss=position_data.get('stop_loss_price'),
                take_profit=position_data.get('take_profit_price')
            )
            
            # Execute the trade
            execution_result = await self.execute_order(trade_order)
            
            # Set up risk management orders (stop loss, take profit)
            if execution_result['status'] == 'FILLED':
                await self.setup_risk_orders(trade_order, execution_result)
                
            return execution_result
            
        except Exception as e:
            logger.error(f"Execution error: {e}")
            return {'status': 'ERROR', 'reason': f'Execution error: {str(e)}'}
    
    async def pre_execution_risk_check(self, symbol: str, position_data: dict, confidence: float) -> dict:
        """Perform comprehensive risk checks before execution"""
        warnings = []
        
        # Check 1: Position size vs account balance
        position_usd = position_data.get('position_usd', 0)
        account_balance = await self.get_account_balance()
        
        if position_usd > account_balance * 0.1:  # Max 10% per trade
            warnings.append(f'Position size ${position_usd:.2f} exceeds 10% of balance')
            
        # Check 2: Leverage limits
        leverage = position_data.get('effective_leverage', 1.0)
        if leverage > 10:  # Max 10x leverage
            return {'approved': False, 'reason': f'Leverage {leverage:.1f}x exceeds maximum 10x'}
            
        # Check 3: Market conditions
        if len(warnings) > 2:
            return {'approved': False, 'reason': 'Too many risk warnings', 'warnings': warnings}
            
        return {'approved': True, 'warnings': warnings}
    
    async def execute_order(self, order: TradeOrder) -> dict:
        """Execute the actual trade order"""
        try:
            # Convert to exchange format
            order_params = {
                'symbol': order.symbol,
                'type': order.order_type.value,
                'side': order.side,
                'amount': order.amount,
            }

            # Add leverage for futures
            if ':' in order.symbol:  # Futures symbol
                order_params['leverage'] = order.leverage

            # Execute order (handle both sync and async exchanges)
            if asyncio.iscoroutinefunction(self.exchange.create_order):
                result = await self.exchange.create_order(**order_params)
            else:
                result = self.exchange.create_order(**order_params)
            
            # Track the order
            self.active_orders[result['id']] = {
                'order': order,
                'result': result,
                'timestamp': time.time()
            }
            
            return {
                'status': 'FILLED',
                'order_id': result['id'],
                'symbol': order.symbol,
                'side': order.side,
                'amount': result['filled'],
                'price': result['average'],
                'fee': result.get('fee', {}),
                'timestamp': result['timestamp']
            }
            
        except Exception as e:
            logger.error(f"Order execution failed: {e}")
            return {
                'status': 'FAILED',
                'reason': str(e),
                'order': order.__dict__
            }
    
    async def setup_risk_orders(self, original_order: TradeOrder, execution_result: dict):
        """Set up stop loss and take profit orders"""
        if not (original_order.stop_loss or original_order.take_profit):
            return
            
        try:
            # Stop Loss Order
            if original_order.stop_loss:
                stop_order = {
                    'symbol': original_order.symbol,
                    'type': 'stop_loss',
                    'side': 'sell' if original_order.side == 'buy' else 'buy',
                    'amount': execution_result['amount'],
                    'stopPrice': original_order.stop_loss
                }
                if asyncio.iscoroutinefunction(self.exchange.create_order):
                    await self.exchange.create_order(**stop_order)
                else:
                    self.exchange.create_order(**stop_order)

            # Take Profit Order
            if original_order.take_profit:
                tp_order = {
                    'symbol': original_order.symbol,
                    'type': 'take_profit',
                    'side': 'sell' if original_order.side == 'buy' else 'buy',
                    'amount': execution_result['amount'],
                    'stopPrice': original_order.take_profit
                }
                if asyncio.iscoroutinefunction(self.exchange.create_order):
                    await self.exchange.create_order(**tp_order)
                else:
                    self.exchange.create_order(**tp_order)
                
        except Exception as e:
            logger.error(f"Failed to set up risk orders: {e}")
    
    async def get_account_balance(self) -> float:
        """Get account balance - implement based on your exchange"""
        try:
            if asyncio.iscoroutinefunction(self.exchange.fetch_balance):
                balance = await self.exchange.fetch_balance()
            else:
                balance = self.exchange.fetch_balance()
            return balance.get('USDT', {}).get('free', 0)
        except:
            return 1000.0  # Fallback value
