#!/usr/bin/env python3
"""
System Integration Test for Epinnox Trading System
Tests all major components and their interactions
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import components to test
try:
    from portfolio.portfolio_manager import PortfolioManager
    from core.llm_orchestrator import LLMPromptOrchestrator
    from data.live_data_manager import LiveDataManager
    from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator, TradingMode
    from utils.fix_unicode_logging import setup_unicode_console, create_safe_logging_config
    print("✅ All imports successful")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_portfolio_manager():
    """Test PortfolioManager functionality"""
    print("\n🧪 Testing PortfolioManager...")
    
    try:
        # Initialize portfolio manager
        pm = PortfolioManager(initial_balance=1000.0)
        
        # Test get_total_value method
        total_value = pm.get_total_value()
        assert total_value == 1000.0, f"Expected 1000.0, got {total_value}"
        
        # Test portfolio summary
        summary = pm.get_portfolio_summary()
        assert 'total_value' in summary, "Portfolio summary missing total_value"
        
        print("✅ PortfolioManager tests passed")
        return True
        
    except Exception as e:
        print(f"❌ PortfolioManager test failed: {e}")
        return False

def test_llm_orchestrator():
    """Test LLMPromptOrchestrator functionality"""
    print("\n🧪 Testing LLMPromptOrchestrator...")
    
    try:
        # Initialize LLM orchestrator (without actual LLM runner)
        llm_orch = LLMPromptOrchestrator(lmstudio_runner=None)
        
        # Test analyze_market method exists
        assert hasattr(llm_orch, 'analyze_market'), "analyze_market method missing"
        
        print("✅ LLMPromptOrchestrator tests passed")
        return True
        
    except Exception as e:
        print(f"❌ LLMPromptOrchestrator test failed: {e}")
        return False

def test_data_manager():
    """Test LiveDataManager functionality"""
    print("\n🧪 Testing LiveDataManager...")
    
    try:
        # Initialize data manager
        dm = LiveDataManager()
        
        # Test method signatures
        assert hasattr(dm, 'get_latest_data'), "get_latest_data method missing"
        assert hasattr(dm, 'get_chart_data'), "get_chart_data method missing"
        
        # Test get_chart_data with limit parameter
        chart_data = dm.get_chart_data('BTC/USDT:USDT', '1m', limit=10)
        assert isinstance(chart_data, list), "get_chart_data should return a list"
        
        print("✅ LiveDataManager tests passed")
        return True
        
    except Exception as e:
        print(f"❌ LiveDataManager test failed: {e}")
        return False

async def test_autonomous_orchestrator():
    """Test AutonomousTradingOrchestrator functionality"""
    print("\n🧪 Testing AutonomousTradingOrchestrator...")
    
    try:
        # Initialize orchestrator in simulation mode
        config = {
            'trading_mode': 'simulation',
            'symbols': ['BTC/USDT:USDT'],
            'initial_balance': 1000.0
        }
        orchestrator = AutonomousTradingOrchestrator(config)

        # Test initialization
        assert orchestrator.mode == TradingMode.SIMULATION, "Mode not set correctly"
        assert 'BTC/USDT:USDT' in orchestrator.active_symbols, "Symbol not in active symbols"
        
        print("✅ AutonomousTradingOrchestrator tests passed")
        return True
        
    except Exception as e:
        print(f"❌ AutonomousTradingOrchestrator test failed: {e}")
        return False

def test_unicode_logging():
    """Test Unicode logging fixes"""
    print("\n🧪 Testing Unicode logging...")
    
    try:
        # Setup Unicode console
        unicode_ok = setup_unicode_console()
        
        # Create safe logging config
        create_safe_logging_config()
        
        # Test logging with emojis
        logger = logging.getLogger(__name__)
        logger.info("🔍 Testing Unicode logging...")
        logger.info("✅ Configuration loaded successfully")
        
        print("✅ Unicode logging tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Unicode logging test failed: {e}")
        return False

async def main():
    """Run all integration tests"""
    print("🚀 Starting Epinnox System Integration Tests")
    print("=" * 50)
    
    # Test results
    results = []
    
    # Test Unicode logging first
    results.append(test_unicode_logging())
    
    # Test individual components
    results.append(test_portfolio_manager())
    results.append(test_llm_orchestrator())
    results.append(test_data_manager())
    results.append(await test_autonomous_orchestrator())
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 Integration Test Results:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! System integration successful!")
        return True
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please review and fix issues.")
        return False

if __name__ == '__main__':
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        sys.exit(1)
