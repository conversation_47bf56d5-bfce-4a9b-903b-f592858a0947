#!/usr/bin/env python3
"""
Risk Management Testing with Live Market Conditions
Tests all risk limits, stop losses, and safety mechanisms with real market volatility
"""

import sys
import os
import asyncio
import json
from datetime import datetime
import logging

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from trading.ccxt_trading_engine import CCXTTradingEngine
from portfolio.portfolio_manager import PortfolioManager
from execution.autonomous_executor import AutonomousTradeExecutor
from monitoring.performance_tracker import PerformanceTracker

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RiskManagementTester:
    """Test risk management with live market conditions"""
    
    def __init__(self):
        self.results = []
        self.exchange_engine = None
        self.portfolio = None
        self.executor = None
        self.live_prices = {}
    
    async def setup_live_environment(self, initial_balance=10000.0):
        """Setup environment with live market data"""
        logger.info(f"Setting up risk management test with live data (${initial_balance:,.2f})")
        
        # Create exchange connection (demo mode for safety)
        self.exchange_engine = CCXTTradingEngine('htx', demo_mode=True)
        if not self.exchange_engine.initialize_exchange():
            raise Exception("Failed to connect to live exchange")
        
        # Create portfolio manager with conservative risk settings
        self.portfolio = PortfolioManager(initial_balance=initial_balance, max_positions=3)

        # Set conservative risk limits
        self.portfolio.set_risk_limits(
            max_portfolio_risk=0.15,  # 15% max risk
            max_position_size=0.08,   # 8% max position size
            max_leverage=2.0,         # 2x max leverage
            max_daily_loss=0.05       # 5% max daily loss
        )
        
        # Create autonomous executor
        self.executor = AutonomousTradeExecutor(self.exchange_engine.exchange)
        
        logger.info("✅ Live risk management environment setup complete")
    
    async def fetch_live_prices(self, symbols):
        """Fetch current live prices"""
        prices = {}
        for symbol in symbols:
            try:
                ticker = self.exchange_engine.exchange.fetch_ticker(symbol)
                prices[symbol] = {
                    'price': ticker['last'],
                    'bid': ticker['bid'],
                    'ask': ticker['ask'],
                    'spread': ticker['ask'] - ticker['bid'],
                    'spread_pct': ((ticker['ask'] - ticker['bid']) / ticker['bid']) * 100
                }
                logger.info(f"Live {symbol}: ${ticker['last']:.2f} (spread: {prices[symbol]['spread_pct']:.3f}%)")
            except Exception as e:
                logger.error(f"Failed to fetch price for {symbol}: {e}")
                prices[symbol] = None
        
        self.live_prices = prices
        return prices
    
    async def test_portfolio_risk_limits(self):
        """Test portfolio risk limit enforcement with live prices"""
        logger.info("🔧 Testing Portfolio Risk Limits with Live Data")
        
        try:
            # Fetch live prices
            symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT']
            await self.fetch_live_prices(symbols)
            
            # Test 1: Try to open position within limits
            btc_price = self.live_prices['BTC/USDT:USDT']['price']
            safe_size = 0.005  # Small position size
            
            try:
                await self.portfolio.open_position('BTC/USDT:USDT', 'long', safe_size, btc_price, 1.0)
                within_limits_success = True
                logger.info(f"✅ Safe position opened: {safe_size} BTC at ${btc_price:.2f}")
            except Exception as e:
                within_limits_success = False
                logger.error(f"❌ Safe position failed: {e}")
            
            # Test 2: Try to open position that exceeds portfolio risk
            eth_price = self.live_prices['ETH/USDT:USDT']['price']
            large_size = 2.0  # Large position that should exceed risk limits
            
            try:
                await self.portfolio.open_position('ETH/USDT:USDT', 'long', large_size, eth_price, 1.0)
                risk_limit_enforced = False
                logger.warning("⚠️ Large position was allowed (risk limits may not be working)")
            except Exception as e:
                risk_limit_enforced = True
                logger.info(f"✅ Risk limits enforced: {e}")
            
            # Calculate current portfolio risk
            current_risk = self.portfolio.calculate_total_portfolio_risk()
            
            result = {
                'test': 'Portfolio Risk Limits',
                'within_limits_success': within_limits_success,
                'risk_limit_enforced': risk_limit_enforced,
                'current_portfolio_risk': current_risk,
                'max_allowed_risk': self.portfolio.max_portfolio_risk,
                'success': within_limits_success and risk_limit_enforced and current_risk <= self.portfolio.max_portfolio_risk
            }
            
            self.results.append(result)
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Portfolio risk limits test failed: {e}")
            return False
    
    async def test_position_sizing_limits(self):
        """Test position sizing limits with live market volatility"""
        logger.info("🔧 Testing Position Sizing Limits")
        
        try:
            # Reset portfolio
            await self.setup_live_environment(5000.0)  # Smaller balance for testing
            
            # Fetch live prices
            await self.fetch_live_prices(['BTC/USDT:USDT'])
            btc_price = self.live_prices['BTC/USDT:USDT']['price']
            
            # Test maximum allowed position size
            max_position_value = self.portfolio.current_balance * self.portfolio.max_position_size
            max_size = max_position_value / btc_price
            
            # Try position at exactly the limit
            try:
                await self.portfolio.open_position('BTC/USDT:USDT', 'long', max_size * 0.9, btc_price, 1.0)
                at_limit_success = True
                logger.info(f"✅ Position at 90% of limit opened successfully")
            except Exception as e:
                at_limit_success = False
                logger.error(f"❌ Position at limit failed: {e}")
            
            # Try position exceeding the limit
            try:
                await self.portfolio.open_position('ETH/USDT:USDT', 'long', max_size * 1.5, btc_price, 1.0)
                over_limit_blocked = False
                logger.warning("⚠️ Over-limit position was allowed")
            except Exception as e:
                over_limit_blocked = True
                logger.info(f"✅ Over-limit position blocked: {e}")
            
            result = {
                'test': 'Position Sizing Limits',
                'max_position_value': max_position_value,
                'at_limit_success': at_limit_success,
                'over_limit_blocked': over_limit_blocked,
                'success': at_limit_success and over_limit_blocked
            }
            
            self.results.append(result)
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Position sizing limits test failed: {e}")
            return False
    
    async def test_leverage_limits(self):
        """Test leverage limits enforcement"""
        logger.info("🔧 Testing Leverage Limits")
        
        try:
            # Reset portfolio
            await self.setup_live_environment(10000.0)
            
            # Fetch live prices
            await self.fetch_live_prices(['BTC/USDT:USDT'])
            btc_price = self.live_prices['BTC/USDT:USDT']['price']
            
            # Test allowed leverage (use smaller position size to stay within 8% limit)
            safe_position_size = (self.portfolio.current_balance * 0.07) / btc_price  # 7% of balance
            try:
                await self.portfolio.open_position('BTC/USDT:USDT', 'long', safe_position_size, btc_price, 2.0)  # 2x leverage (at limit)
                allowed_leverage_success = True
                logger.info("✅ Allowed leverage (2x) position opened")
            except Exception as e:
                allowed_leverage_success = False
                logger.error(f"❌ Allowed leverage failed: {e}")

            # Test excessive leverage (should be blocked)
            try:
                await self.portfolio.open_position('ETH/USDT:USDT', 'long', safe_position_size, 3000.0, 5.0)  # 5x leverage (over limit)
                excessive_leverage_blocked = False
                logger.warning("⚠️ Excessive leverage was allowed")
            except Exception as e:
                excessive_leverage_blocked = True
                logger.info(f"✅ Excessive leverage blocked: {e}")
            
            result = {
                'test': 'Leverage Limits',
                'max_allowed_leverage': self.portfolio.max_leverage,
                'allowed_leverage_success': allowed_leverage_success,
                'excessive_leverage_blocked': excessive_leverage_blocked,
                'success': allowed_leverage_success and excessive_leverage_blocked
            }
            
            self.results.append(result)
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Leverage limits test failed: {e}")
            return False
    
    async def test_stop_loss_with_volatility(self):
        """Test stop loss functionality with real market volatility"""
        logger.info("🔧 Testing Stop Loss with Market Volatility")
        
        try:
            # Reset portfolio
            await self.setup_live_environment(10000.0)
            
            # Fetch live prices
            await self.fetch_live_prices(['BTC/USDT:USDT'])
            btc_price = self.live_prices['BTC/USDT:USDT']['price']
            
            # Open position with stop loss (use smaller position size)
            safe_position_size = (self.portfolio.current_balance * 0.07) / btc_price  # 7% of balance
            stop_loss_price = btc_price * 0.95  # 5% stop loss
            take_profit_price = btc_price * 1.10  # 10% take profit

            await self.portfolio.open_position('BTC/USDT:USDT', 'long', safe_position_size, btc_price, 1.0,
                                             stop_loss=stop_loss_price, take_profit=take_profit_price)
            
            # Simulate price movement below stop loss
            simulated_low_price = btc_price * 0.93  # 7% drop
            
            # Check if stop loss would trigger
            position = self.portfolio.positions.get('BTC/USDT:USDT')
            stop_loss_would_trigger = position and position.stop_loss and simulated_low_price <= position.stop_loss
            
            result = {
                'test': 'Stop Loss with Volatility',
                'entry_price': btc_price,
                'stop_loss_price': stop_loss_price,
                'simulated_price': simulated_low_price,
                'stop_loss_would_trigger': stop_loss_would_trigger,
                'success': stop_loss_would_trigger
            }
            
            self.results.append(result)
            logger.info(f"✅ Stop loss test: Would trigger at ${simulated_low_price:.2f} (SL: ${stop_loss_price:.2f})")
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Stop loss test failed: {e}")
            return False
    
    def generate_risk_report(self):
        """Generate comprehensive risk management report"""
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'total_tests': len(self.results),
            'passed_tests': sum(1 for r in self.results if r['success']),
            'failed_tests': sum(1 for r in self.results if not r['success']),
            'success_rate': sum(1 for r in self.results if r['success']) / len(self.results) * 100 if self.results else 0,
            'live_market_data': self.live_prices,
            'risk_tests': self.results
        }
        
        # Save report
        filename = f"risk_management_live_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        return report, filename

async def main():
    """Run all risk management tests with live conditions"""
    print("🚀 Starting Risk Management Testing with Live Market Conditions")
    print("=" * 70)
    
    tester = RiskManagementTester()
    
    # Setup live environment
    try:
        await tester.setup_live_environment()
    except Exception as e:
        print(f"❌ Failed to setup live environment: {e}")
        return False
    
    # Run all risk management tests
    tests = [
        tester.test_portfolio_risk_limits,
        tester.test_position_sizing_limits,
        tester.test_leverage_limits,
        tester.test_stop_loss_with_volatility,
    ]
    
    results = []
    for i, test in enumerate(tests, 1):
        print(f"\n🔧 Running risk test {i}/{len(tests)}...")
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Risk test {i} failed with exception: {e}")
            results.append(False)
    
    # Generate report
    report, filename = tester.generate_risk_report()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 RISK MANAGEMENT TESTING SUMMARY")
    print("=" * 70)
    
    for i, (test_result, success) in enumerate(zip(tester.results, results), 1):
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - Test {i}: {test_result['test']}")
    
    print(f"\nResults: {report['passed_tests']}/{report['total_tests']} tests passed")
    print(f"Success Rate: {report['success_rate']:.1f}%")
    print(f"📄 Detailed report saved to: {filename}")
    
    if report['success_rate'] >= 75:
        print("🎉 Risk management testing with live conditions PASSED!")
        return True
    else:
        print("⚠️ Some risk management tests failed. Review the detailed report.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
