# Epinnox Live Production Trading Configuration
# Account Balance: $50 USD
# Trading Mode: Live with Real Funds

# Core Trading Settings
trading_mode: live
initial_balance: 50.0

# Trading Symbols - Major pairs only for conservative approach
symbols:
  - BTC/USDT:USDT
  - ETH/USDT:USDT

# Risk Management Settings (Conservative for $50 account)
max_daily_loss: -10.0          # 20% of account balance
max_position_size: 15.0        # 30% of account balance  
max_leverage: 3.0              # Conservative leverage
max_open_positions: 2          # Maximum 2 concurrent positions
min_account_balance: 5.0       # 10% of account as minimum threshold
max_drawdown: -40.0            # 40% maximum drawdown

# Position Sizing Strategy
position_sizing:
  base_size_pct: 0.15          # 15% of account per position base
  confidence_multiplier: 1.5   # Multiply by confidence (max 1.5x)
  min_position_usd: 5.0        # Minimum $5 position
  max_position_usd: 15.0       # Maximum $15 position

# Exchange Configuration
exchange:
  name: htx                    # HTX exchange
  demo_mode: false             # LIVE TRADING MODE
  futures_enabled: true        # Enable futures trading
  api_credentials:
    # API credentials will be loaded from environment variables
    # HTX_API_KEY, HTX_SECRET_KEY, HTX_PASSPHRASE
    use_env_vars: true

# LLM Configuration - Conservative settings
llm_config:
  model: phi-3.1-mini
  temperature: 0.2             # Lower temperature for more conservative decisions
  max_tokens: 256              # Shorter responses for faster processing
  confidence_threshold: 0.7    # Higher confidence threshold for live trading

# Trading Strategy Parameters
strategy:
  decision_confidence_min: 0.65    # Minimum confidence to execute trades
  risk_reward_ratio_min: 1.5       # Minimum 1.5:1 risk/reward ratio
  max_correlation_threshold: 0.7   # Avoid highly correlated positions
  stop_loss_pct: 0.02             # 2% stop loss
  take_profit_pct: 0.04            # 4% take profit (2:1 ratio)

# Safety and Monitoring Configuration
monitoring:
  health_check_interval: 15        # Check system health every 15 seconds
  metrics_collection_interval: 5   # Collect metrics every 5 seconds
  alert_processing_interval: 3     # Process alerts every 3 seconds
  balance_check_interval: 10       # Check account balance every 10 seconds
  
# Alert Thresholds
alerts:
  critical_balance_threshold: 10.0     # Alert if balance drops below $10
  high_loss_threshold: -5.0            # Alert if daily loss exceeds $5
  position_size_warning: 12.0          # Warn if position size exceeds $12
  leverage_warning: 2.5                # Warn if leverage exceeds 2.5x

# Emergency Procedures
emergency:
  auto_stop_on_daily_loss: true        # Auto-stop if daily loss limit hit
  auto_stop_on_low_balance: true       # Auto-stop if balance too low
  auto_stop_on_connection_loss: true   # Auto-stop if exchange connection lost
  emergency_close_all_positions: true  # Close all positions on emergency
  
# Logging Configuration
logging:
  level: INFO
  log_trades: true
  log_decisions: true
  log_performance: true
  log_safety_events: true
  max_log_files: 10
  log_rotation_mb: 50

# Performance Tracking
performance:
  track_pnl: true
  track_win_rate: true
  track_sharpe_ratio: true
  track_max_drawdown: true
  daily_performance_report: true

# Data and Connectivity
data:
  websocket_enabled: true
  backup_rest_api: true
  data_timeout_seconds: 30
  reconnect_attempts: 5
  reconnect_delay_seconds: 10

# Trading Hours (24/7 for crypto)
trading_hours:
  enabled: true
  start_hour: 0
  end_hour: 24
  timezone: UTC
  
# Backup and Recovery
backup:
  save_state_interval: 300     # Save state every 5 minutes
  backup_trades: true
  backup_performance: true
