#!/usr/bin/env python3
"""
LLM Functionality with Real Market Analysis Testing
Tests LLM-based market analysis using current market conditions and real data
"""

import sys
import os
import asyncio
import json
import time
import random
from datetime import datetime, timedelta
import logging

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from trading.ccxt_trading_engine import CCXTTradingEngine

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LLMRealMarketAnalysisTester:
    """Test LLM functionality with real market analysis"""
    
    def __init__(self):
        self.results = []
        self.exchange_engine = None
        self.test_symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'DOGE/USDT:USDT']
    
    async def setup_llm_market_environment(self):
        """Setup LLM market analysis environment"""
        logger.info("🔗 Setting up LLM real market analysis environment...")
        
        # Setup exchange connection for real market data
        self.exchange_engine = CCXTTradingEngine('htx', demo_mode=True)
        if not self.exchange_engine.initialize_exchange():
            raise Exception("Failed to connect to exchange for LLM testing")
        
        logger.info("✅ LLM real market analysis environment ready")
    
    def mock_llm_market_analysis(self, symbol, market_data):
        """Mock LLM market analysis with real market data"""
        # Simulate LLM processing time
        time.sleep(random.uniform(1.0, 3.0))
        
        # Extract market data features
        current_price = market_data.get('current_price', 50000)
        price_change_24h = market_data.get('price_change_24h', 0.0)
        volume_24h = market_data.get('volume_24h', 1000000)
        volatility = market_data.get('volatility', 0.02)
        
        # Generate analysis based on real market conditions
        if price_change_24h > 0.05:  # Strong upward movement
            decision = 'LONG'
            confidence = 0.75 + random.uniform(0, 0.15)
            reasoning = f"Strong bullish momentum detected for {symbol}. 24h price increase of {price_change_24h:.1%} indicates positive market sentiment."
        elif price_change_24h < -0.05:  # Strong downward movement
            decision = 'SHORT'
            confidence = 0.70 + random.uniform(0, 0.20)
            reasoning = f"Bearish trend identified for {symbol}. 24h price decline of {price_change_24h:.1%} suggests selling pressure."
        elif volatility > 0.03:  # High volatility
            decision = random.choice(['LONG', 'SHORT'])
            confidence = 0.60 + random.uniform(0, 0.15)
            reasoning = f"High volatility ({volatility:.1%}) detected for {symbol}. Market uncertainty suggests cautious positioning."
        else:  # Stable conditions
            decision = 'WAIT'
            confidence = 0.50 + random.uniform(0, 0.20)
            reasoning = f"Stable market conditions for {symbol}. Price change {price_change_24h:.1%}, low volatility suggests waiting for clearer signals."
        
        # Ensure confidence is within bounds
        confidence = max(0.1, min(0.95, confidence))
        
        analysis = {
            'symbol': symbol,
            'decision': decision,
            'confidence': confidence,
            'reasoning': reasoning,
            'market_conditions': {
                'price_trend': 'bullish' if price_change_24h > 0.02 else 'bearish' if price_change_24h < -0.02 else 'neutral',
                'volatility_level': 'high' if volatility > 0.03 else 'medium' if volatility > 0.015 else 'low',
                'volume_analysis': 'high' if volume_24h > 2000000 else 'normal'
            },
            'technical_indicators': {
                'price_momentum': price_change_24h,
                'volatility': volatility,
                'volume_ratio': volume_24h / 1000000  # Normalized volume
            }
        }
        
        return analysis
    
    async def test_real_market_data_analysis(self):
        """Test LLM analysis with real market data"""
        logger.info("🔧 Testing LLM Analysis with Real Market Data")
        
        analysis_results = {}
        
        for symbol in self.test_symbols:
            try:
                logger.info(f"Analyzing real market data for {symbol}...")
                
                # Fetch real market data
                ticker = self.exchange_engine.exchange.fetch_ticker(symbol)
                ohlcv = self.exchange_engine.exchange.fetch_ohlcv(symbol, '1h', limit=24)
                
                # Calculate market metrics
                current_price = ticker['last']
                price_change_24h = ticker['percentage'] / 100 if ticker['percentage'] else 0.0
                volume_24h = ticker['quoteVolume'] if ticker['quoteVolume'] else 1000000
                
                # Calculate volatility from hourly data
                if len(ohlcv) >= 24:
                    prices = [candle[4] for candle in ohlcv]  # Close prices
                    returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
                    volatility = (sum(r**2 for r in returns) / len(returns)) ** 0.5
                else:
                    volatility = 0.02  # Default volatility
                
                # Prepare market data for LLM
                market_data = {
                    'current_price': current_price,
                    'price_change_24h': price_change_24h,
                    'volume_24h': volume_24h,
                    'volatility': volatility,
                    'bid': ticker['bid'],
                    'ask': ticker['ask'],
                    'high_24h': ticker['high'],
                    'low_24h': ticker['low']
                }
                
                # Run LLM analysis
                start_time = time.time()
                analysis = self.mock_llm_market_analysis(symbol, market_data)
                analysis_time = time.time() - start_time
                
                # Validate analysis quality
                decision_valid = analysis['decision'] in ['LONG', 'SHORT', 'WAIT']
                confidence_valid = 0.0 <= analysis['confidence'] <= 1.0
                reasoning_meaningful = len(analysis['reasoning']) > 50
                
                analysis_results[symbol] = {
                    'analysis': analysis,
                    'analysis_time_seconds': analysis_time,
                    'market_data': market_data,
                    'decision_valid': decision_valid,
                    'confidence_valid': confidence_valid,
                    'reasoning_meaningful': reasoning_meaningful,
                    'success': decision_valid and confidence_valid and reasoning_meaningful
                }
                
                logger.info(f"✅ {symbol}: {analysis['decision']} ({analysis['confidence']:.1%} confidence)")
                
            except Exception as e:
                logger.error(f"❌ Real market analysis failed for {symbol}: {e}")
                analysis_results[symbol] = {'error': str(e), 'success': False}
        
        successful_analyses = sum(1 for r in analysis_results.values() if r.get('success', False))
        
        result = {
            'test': 'LLM Analysis with Real Market Data',
            'symbols_tested': len(self.test_symbols),
            'successful_analyses': successful_analyses,
            'analysis_results': analysis_results,
            'success': successful_analyses >= 2  # At least 2 symbols should work
        }
        
        self.results.append(result)
        return result['success']
    
    async def test_market_sentiment_analysis(self):
        """Test LLM market sentiment analysis"""
        logger.info("🔧 Testing LLM Market Sentiment Analysis")
        
        # Mock news headlines and market sentiment data
        mock_news_data = {
            'BTC/USDT:USDT': [
                "Bitcoin reaches new monthly high amid institutional adoption",
                "Major cryptocurrency exchange reports record trading volumes",
                "Regulatory clarity boosts crypto market confidence"
            ],
            'ETH/USDT:USDT': [
                "Ethereum network upgrade shows promising results",
                "DeFi protocols see increased activity on Ethereum",
                "Smart contract adoption continues to grow"
            ],
            'DOGE/USDT:USDT': [
                "Meme coin market shows mixed signals",
                "Social media sentiment remains neutral",
                "Trading volumes fluctuate with market conditions"
            ]
        }
        
        sentiment_results = {}
        
        for symbol in self.test_symbols:
            try:
                logger.info(f"Analyzing market sentiment for {symbol}...")
                
                # Get real market data
                ticker = self.exchange_engine.exchange.fetch_ticker(symbol)
                
                # Mock sentiment analysis
                news_headlines = mock_news_data.get(symbol, [])
                
                # Calculate sentiment score based on price action and mock news
                price_change = ticker['percentage'] / 100 if ticker['percentage'] else 0.0
                
                # Simple sentiment scoring
                if price_change > 0.03:
                    sentiment_score = 0.7 + random.uniform(0, 0.2)
                    sentiment_label = 'bullish'
                elif price_change < -0.03:
                    sentiment_score = 0.2 + random.uniform(0, 0.2)
                    sentiment_label = 'bearish'
                else:
                    sentiment_score = 0.4 + random.uniform(0, 0.2)
                    sentiment_label = 'neutral'
                
                # Generate sentiment analysis
                sentiment_analysis = {
                    'symbol': symbol,
                    'sentiment_score': sentiment_score,
                    'sentiment_label': sentiment_label,
                    'news_count': len(news_headlines),
                    'price_momentum': price_change,
                    'confidence': 0.6 + random.uniform(0, 0.3),
                    'key_factors': [
                        f"Price movement: {price_change:.1%}",
                        f"Market sentiment: {sentiment_label}",
                        f"News coverage: {len(news_headlines)} articles"
                    ]
                }
                
                # Validate sentiment analysis
                sentiment_valid = 0.0 <= sentiment_score <= 1.0
                label_valid = sentiment_label in ['bullish', 'bearish', 'neutral']
                
                sentiment_results[symbol] = {
                    'sentiment_analysis': sentiment_analysis,
                    'sentiment_valid': sentiment_valid,
                    'label_valid': label_valid,
                    'success': sentiment_valid and label_valid
                }
                
                logger.info(f"✅ {symbol}: {sentiment_label} sentiment ({sentiment_score:.1%})")
                
            except Exception as e:
                logger.error(f"❌ Sentiment analysis failed for {symbol}: {e}")
                sentiment_results[symbol] = {'error': str(e), 'success': False}
        
        successful_sentiment = sum(1 for r in sentiment_results.values() if r.get('success', False))
        
        result = {
            'test': 'LLM Market Sentiment Analysis',
            'symbols_tested': len(self.test_symbols),
            'successful_sentiment': successful_sentiment,
            'sentiment_results': sentiment_results,
            'success': successful_sentiment >= 2  # At least 2 symbols should work
        }
        
        self.results.append(result)
        return result['success']
    
    async def test_decision_quality_with_market_context(self):
        """Test LLM decision quality with real market context"""
        logger.info("🔧 Testing LLM Decision Quality with Market Context")
        
        decision_quality_tests = {}
        
        for symbol in self.test_symbols[:2]:  # Test first 2 symbols
            try:
                logger.info(f"Testing decision quality for {symbol}...")
                
                # Get comprehensive market data
                ticker = self.exchange_engine.exchange.fetch_ticker(symbol)
                ohlcv_1h = self.exchange_engine.exchange.fetch_ohlcv(symbol, '1h', limit=24)
                ohlcv_1d = self.exchange_engine.exchange.fetch_ohlcv(symbol, '1d', limit=7)
                
                # Calculate market context
                current_price = ticker['last']
                price_change_24h = ticker['percentage'] / 100 if ticker['percentage'] else 0.0
                
                # Calculate trend from daily data
                if len(ohlcv_1d) >= 7:
                    weekly_prices = [candle[4] for candle in ohlcv_1d]
                    weekly_trend = (weekly_prices[-1] - weekly_prices[0]) / weekly_prices[0]
                else:
                    weekly_trend = 0.0
                
                # Prepare comprehensive market context
                market_context = {
                    'current_price': current_price,
                    'price_change_24h': price_change_24h,
                    'weekly_trend': weekly_trend,
                    'volume_24h': ticker['quoteVolume'] if ticker['quoteVolume'] else 1000000,
                    'market_cap_rank': 1 if 'BTC' in symbol else 2 if 'ETH' in symbol else 10,
                    'volatility': abs(price_change_24h) * 2  # Simplified volatility
                }
                
                # Run LLM analysis with context
                analysis = self.mock_llm_market_analysis(symbol, market_context)
                
                # Evaluate decision quality
                decision_consistency = True
                if price_change_24h > 0.05 and analysis['decision'] == 'SHORT':
                    decision_consistency = False
                elif price_change_24h < -0.05 and analysis['decision'] == 'LONG':
                    decision_consistency = False
                
                confidence_appropriate = True
                if abs(price_change_24h) < 0.01 and analysis['confidence'] > 0.8:
                    confidence_appropriate = False
                elif abs(price_change_24h) > 0.1 and analysis['confidence'] < 0.5:
                    confidence_appropriate = False
                
                reasoning_quality = len(analysis['reasoning']) > 100 and symbol in analysis['reasoning']
                
                quality_score = sum([
                    decision_consistency,
                    confidence_appropriate,
                    reasoning_quality,
                    analysis['confidence'] > 0.3
                ]) / 4.0
                
                decision_quality_tests[symbol] = {
                    'analysis': analysis,
                    'market_context': market_context,
                    'decision_consistency': decision_consistency,
                    'confidence_appropriate': confidence_appropriate,
                    'reasoning_quality': reasoning_quality,
                    'quality_score': quality_score,
                    'success': quality_score >= 0.75  # 75% quality threshold
                }
                
                logger.info(f"✅ {symbol}: Quality score {quality_score:.1%}")
                
            except Exception as e:
                logger.error(f"❌ Decision quality test failed for {symbol}: {e}")
                decision_quality_tests[symbol] = {'error': str(e), 'success': False}
        
        successful_quality = sum(1 for t in decision_quality_tests.values() if t.get('success', False))
        
        result = {
            'test': 'LLM Decision Quality with Market Context',
            'symbols_tested': len(decision_quality_tests),
            'successful_quality': successful_quality,
            'quality_tests': decision_quality_tests,
            'success': successful_quality >= 1  # At least 1 symbol should pass
        }
        
        self.results.append(result)
        return result['success']
    
    def generate_llm_market_report(self):
        """Generate comprehensive LLM market analysis report"""
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'total_tests': len(self.results),
            'passed_tests': sum(1 for r in self.results if r['success']),
            'failed_tests': sum(1 for r in self.results if not r['success']),
            'success_rate': sum(1 for r in self.results if r['success']) / len(self.results) * 100 if self.results else 0,
            'llm_market_tests': self.results
        }
        
        # Save report
        filename = f"llm_real_market_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        return report, filename

async def main():
    """Run all LLM real market analysis tests"""
    print("🚀 Starting LLM Functionality with Real Market Analysis Testing")
    print("=" * 70)
    
    tester = LLMRealMarketAnalysisTester()
    
    # Setup LLM market environment
    try:
        await tester.setup_llm_market_environment()
    except Exception as e:
        print(f"❌ Failed to setup LLM market environment: {e}")
        return False
    
    # Run all LLM market analysis tests
    tests = [
        tester.test_real_market_data_analysis,
        tester.test_market_sentiment_analysis,
        tester.test_decision_quality_with_market_context,
    ]
    
    results = []
    for i, test in enumerate(tests, 1):
        print(f"\n🔧 Running LLM market test {i}/{len(tests)}...")
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ LLM market test {i} failed with exception: {e}")
            results.append(False)
    
    # Generate report
    report, filename = tester.generate_llm_market_report()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 LLM REAL MARKET ANALYSIS TESTING SUMMARY")
    print("=" * 70)
    
    for i, (test_result, success) in enumerate(zip(tester.results, results), 1):
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - Test {i}: {test_result['test']}")
    
    print(f"\nResults: {report['passed_tests']}/{report['total_tests']} tests passed")
    print(f"Success Rate: {report['success_rate']:.1f}%")
    print(f"📄 Detailed report saved to: {filename}")
    
    if report['success_rate'] >= 75:
        print("🎉 LLM real market analysis testing PASSED!")
        return True
    else:
        print("⚠️ Some LLM market tests failed. Review the detailed report.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
