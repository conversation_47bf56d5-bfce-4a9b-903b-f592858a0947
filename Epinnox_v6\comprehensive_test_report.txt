============================= test session starts =============================
platform win32 -- Python 3.9.13, pytest-8.4.1, pluggy-1.5.0 -- C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe
cachedir: .pytest_cache
metadata: {'Python': '3.9.13', 'Platform': 'Windows-10-10.0.26100-SP0', 'Packages': {'pytest': '8.4.1', 'pluggy': '1.5.0'}, 'Plugins': {'anyio': '4.9.0', 'dash': '3.0.4', 'asyncio': '0.26.0', 'cov': '6.2.1', 'html': '4.1.1', 'metadata': '3.1.1', 'mock': '3.14.1'}}
rootdir: C:\Users\<USER>\Documents\dev\Epinnox_v6
configfile: pytest.ini
plugins: anyio-4.9.0, dash-3.0.4, asyncio-0.26.0, cov-6.2.1, html-4.1.1, metadata-3.1.1, mock-3.14.1
asyncio: mode=strict, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function
collecting ... collected 44 items

tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_trading_decision_high_confidence PASSED [  2%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_trading_decision_low_confidence PASSED [  4%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_trading_decision_wait PASSED [  6%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_pre_execution_risk_check_excessive_leverage PASSED [  9%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_pre_execution_risk_check_large_position PASSED [ 11%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_order_success PASSED [ 13%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_order_with_futures_symbol PASSED [ 15%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_get_account_balance PASSED [ 18%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_setup_risk_orders PASSED [ 20%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_trade_order_creation PASSED [ 22%]
tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_missing_position_sizing_data PASSED [ 25%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_init_database PASSED [ 27%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_record_trade PASSED [ 29%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_daily_metrics_empty PASSED [ 31%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_daily_metrics_with_trades PASSED [ 34%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_sharpe_ratio PASSED [ 36%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_sharpe_ratio_empty PASSED [ 38%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_max_drawdown PASSED [ 40%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_profit_factor PASSED [ 43%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_profit_factor_no_losses PASSED [ 45%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_get_model_performance_empty PASSED [ 47%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_get_model_performance_with_data PASSED [ 50%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary PASSED [ 52%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_empty PASSED [ 54%]
tests/test_performance_tracker.py::TestPerformanceTracker::test_save_daily_metrics PASSED [ 56%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_can_open_position_success PASSED [ 59%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_can_open_position_max_positions PASSED [ 61%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_can_open_position_excessive_size PASSED [ 63%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_can_open_position_excessive_leverage PASSED [ 65%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_open_position_success FAILED [ 68%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_open_position_switch_sides FAILED [ 70%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_close_position_long_profit FAILED [ 72%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_close_position_short_profit FAILED [ 75%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_close_position_loss FAILED [ 77%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_close_nonexistent_position PASSED [ 79%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_update_positions FAILED [ 81%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_check_risk_levels_stop_loss FAILED [ 84%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_check_risk_levels_take_profit FAILED [ 86%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_calculate_total_portfolio_risk FAILED [ 88%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_get_portfolio_summary PASSED [ 90%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_reset_daily_pnl PASSED [ 93%]
tests/test_portfolio_manager.py::TestPortfolioManager::test_set_risk_limits PASSED [ 95%]
tests/test_integration.py::TestIntegration::test_end_to_end_workflow FAILED [ 97%]
tests/test_integration.py::TestIntegration::test_simulation_trader FAILED [100%]

================================== FAILURES ===================================
_______________ TestPortfolioManager.test_open_position_success _______________
tests\test_portfolio_manager.py:57: in test_open_position_success
    position = await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0)
portfolio\portfolio_manager.py:116: in open_position
    raise Exception(f"Cannot open position: {check_result['reason']}")
E   Exception: Cannot open position: Position size 500.0% exceeds limit 10.0%
----------------------------- Captured log setup ------------------------------
INFO     portfolio.portfolio_manager:portfolio_manager.py:53 Portfolio manager initialized with $1000.00 balance
____________ TestPortfolioManager.test_open_position_switch_sides _____________
tests\test_portfolio_manager.py:70: in test_open_position_switch_sides
    await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0)
portfolio\portfolio_manager.py:116: in open_position
    raise Exception(f"Cannot open position: {check_result['reason']}")
E   Exception: Cannot open position: Position size 500.0% exceeds limit 10.0%
----------------------------- Captured log setup ------------------------------
INFO     portfolio.portfolio_manager:portfolio_manager.py:53 Portfolio manager initialized with $1000.00 balance
____________ TestPortfolioManager.test_close_position_long_profit _____________
tests\test_portfolio_manager.py:82: in test_close_position_long_profit
    await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0)
portfolio\portfolio_manager.py:116: in open_position
    raise Exception(f"Cannot open position: {check_result['reason']}")
E   Exception: Cannot open position: Position size 500.0% exceeds limit 10.0%
----------------------------- Captured log setup ------------------------------
INFO     portfolio.portfolio_manager:portfolio_manager.py:53 Portfolio manager initialized with $1000.00 balance
____________ TestPortfolioManager.test_close_position_short_profit ____________
tests\test_portfolio_manager.py:94: in test_close_position_short_profit
    await portfolio.open_position('ETH/USDT', 'short', 1.0, 3000.0, 1.0)
portfolio\portfolio_manager.py:116: in open_position
    raise Exception(f"Cannot open position: {check_result['reason']}")
E   Exception: Cannot open position: Position size 300.0% exceeds limit 10.0%
----------------------------- Captured log setup ------------------------------
INFO     portfolio.portfolio_manager:portfolio_manager.py:53 Portfolio manager initialized with $1000.00 balance
________________ TestPortfolioManager.test_close_position_loss ________________
tests\test_portfolio_manager.py:104: in test_close_position_loss
    await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0)
portfolio\portfolio_manager.py:116: in open_position
    raise Exception(f"Cannot open position: {check_result['reason']}")
E   Exception: Cannot open position: Position size 500.0% exceeds limit 10.0%
----------------------------- Captured log setup ------------------------------
INFO     portfolio.portfolio_manager:portfolio_manager.py:53 Portfolio manager initialized with $1000.00 balance
_________________ TestPortfolioManager.test_update_positions __________________
tests\test_portfolio_manager.py:122: in test_update_positions
    await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0)
portfolio\portfolio_manager.py:116: in open_position
    raise Exception(f"Cannot open position: {check_result['reason']}")
E   Exception: Cannot open position: Position size 500.0% exceeds limit 10.0%
----------------------------- Captured log setup ------------------------------
INFO     portfolio.portfolio_manager:portfolio_manager.py:53 Portfolio manager initialized with $1000.00 balance
____________ TestPortfolioManager.test_check_risk_levels_stop_loss ____________
tests\test_portfolio_manager.py:143: in test_check_risk_levels_stop_loss
    await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0, stop_loss=49000.0)
portfolio\portfolio_manager.py:116: in open_position
    raise Exception(f"Cannot open position: {check_result['reason']}")
E   Exception: Cannot open position: Position size 500.0% exceeds limit 10.0%
----------------------------- Captured log setup ------------------------------
INFO     portfolio.portfolio_manager:portfolio_manager.py:53 Portfolio manager initialized with $1000.00 balance
___________ TestPortfolioManager.test_check_risk_levels_take_profit ___________
tests\test_portfolio_manager.py:155: in test_check_risk_levels_take_profit
    await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0, take_profit=52000.0)
portfolio\portfolio_manager.py:116: in open_position
    raise Exception(f"Cannot open position: {check_result['reason']}")
E   Exception: Cannot open position: Position size 500.0% exceeds limit 10.0%
----------------------------- Captured log setup ------------------------------
INFO     portfolio.portfolio_manager:portfolio_manager.py:53 Portfolio manager initialized with $1000.00 balance
__________ TestPortfolioManager.test_calculate_total_portfolio_risk ___________
tests\test_portfolio_manager.py:196: in test_calculate_total_portfolio_risk
    assert abs(risk - 0.14625) < 0.001
E   assert 14.47875 < 0.001
E    +  where 14.47875 = abs((14.625 - 0.14625))
----------------------------- Captured log setup ------------------------------
INFO     portfolio.portfolio_manager:portfolio_manager.py:53 Portfolio manager initialized with $1000.00 balance
__________________ TestIntegration.test_end_to_end_workflow ___________________
tests\test_integration.py:132: in test_end_to_end_workflow
    result = process_market_data(
E   TypeError: process_market_data() got an unexpected keyword argument 'combined_data'
------------------------------ Captured log call ------------------------------
INFO     data.exchange:exchange.py:318 Cached markets data
INFO     data.exchange:exchange.py:110 Connected to htx exchange (spot and futures)
INFO     core.multi_timeframe:multi_timeframe.py:53 Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
INFO     core.multi_timeframe:multi_timeframe.py:54 Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
INFO     core.signal_scoring:signal_scoring.py:44 Initialized signal scoring system with smoothing=3, longer_timeframe=True
___________________ TestIntegration.test_simulation_trader ____________________
tests\test_integration.py:211: in test_simulation_trader
    self.simulation_trader.process_signal(signal)
E   AttributeError: 'SimulatedTrader' object has no attribute 'process_signal'
------------------------------ Captured log call ------------------------------
INFO     data.exchange:exchange.py:318 Cached markets data
INFO     data.exchange:exchange.py:110 Connected to htx exchange (spot and futures)
INFO     core.multi_timeframe:multi_timeframe.py:53 Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
INFO     core.multi_timeframe:multi_timeframe.py:54 Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
INFO     core.signal_scoring:signal_scoring.py:44 Initialized signal scoring system with smoothing=3, longer_timeframe=True
INFO     core.signal_scoring:signal_scoring.py:85 Calculated signal scores: {'macd_score': np.float64(-0.15), 'orderbook_score': 0.0, 'volume_score': np.float64(0.04619694023381327), 'price_action_score': np.float64(0.028760198231875686), 'trend_score': 0.0, 'total_score': np.float64(-0.07504286153431104), 'confidence': np.float64(47.654910577052775), 'alignment': 62.5}
============================== warnings summary ===============================
..\..\..\AppData\Local\Programs\Python\Python39\lib\site-packages\transformers\utils\generic.py:441
  C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\transformers\utils\generic.py:441: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
    _torch_pytree._register_pytree_node(

..\..\..\AppData\Local\Programs\Python\Python39\lib\site-packages\transformers\utils\generic.py:309
  C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\transformers\utils\generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
    _torch_pytree._register_pytree_node(

tests/test_performance_tracker.py::TestPerformanceTracker::test_get_model_performance_with_data
  C:\Users\<USER>\Documents\dev\Epinnox_v6\monitoring\performance_tracker.py:213: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.
    confidence_performance = df.groupby('confidence_bucket')['pnl_usd'].agg(['count', 'mean', 'sum']).to_dict()

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
=========================== short test summary info ===========================
FAILED tests/test_portfolio_manager.py::TestPortfolioManager::test_open_position_success
FAILED tests/test_portfolio_manager.py::TestPortfolioManager::test_open_position_switch_sides
FAILED tests/test_portfolio_manager.py::TestPortfolioManager::test_close_position_long_profit
FAILED tests/test_portfolio_manager.py::TestPortfolioManager::test_close_position_short_profit
FAILED tests/test_portfolio_manager.py::TestPortfolioManager::test_close_position_loss
FAILED tests/test_portfolio_manager.py::TestPortfolioManager::test_update_positions
FAILED tests/test_portfolio_manager.py::TestPortfolioManager::test_check_risk_levels_stop_loss
FAILED tests/test_portfolio_manager.py::TestPortfolioManager::test_check_risk_levels_take_profit
FAILED tests/test_portfolio_manager.py::TestPortfolioManager::test_calculate_total_portfolio_risk
FAILED tests/test_integration.py::TestIntegration::test_end_to_end_workflow
FAILED tests/test_integration.py::TestIntegration::test_simulation_trader - A...
================= 11 failed, 33 passed, 3 warnings in 25.24s ==================
