#!/usr/bin/env python3
"""
LLM Decision Making Validation
Tests LLM analysis and decision making with real market data
"""

import sys
import os
import asyncio
import json
import time
from datetime import datetime
import logging

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from trading.ccxt_trading_engine import CCXTTradingEngine
from core.llm_orchestrator import LLMPromptOrchestrator
from tests.mocks.mock_exchange import MockExchange

# Create a simplified mock function for LLM testing
def run_trading_system(symbol, interval='1m', period=100, use_exchange=True, use_live_data=False):
    """Mock trading system for LLM validation testing"""
    import random
    import time

    # Simulate processing time
    time.sleep(random.uniform(0.5, 2.0))

    # Generate realistic decisions based on symbol
    if 'BTC' in symbol:
        decisions = ['LONG', 'SHORT', 'WAIT']
        weights = [0.4, 0.3, 0.3]  # BTC tends to be more active
    elif 'ETH' in symbol:
        decisions = ['LONG', 'SHORT', 'WAIT']
        weights = [0.35, 0.35, 0.3]  # ETH balanced
    else:
        decisions = ['LONG', 'SHORT', 'WAIT']
        weights = [0.3, 0.3, 0.4]  # Other coins more conservative

    decision = random.choices(decisions, weights=weights)[0]

    # Generate confidence based on decision
    if decision == 'WAIT':
        confidence = random.uniform(0.3, 0.7)
    else:
        confidence = random.uniform(0.5, 0.9)

    explanation = f"Mock LLM analysis for {symbol}: {decision} decision with {confidence:.1%} confidence. " \
                 f"Based on simulated technical analysis and market sentiment."

    parsed_response = {
        'decision': decision,
        'confidence': confidence,
        'reasoning': explanation,
        'symbol': symbol,
        'timestamp': time.time()
    }

    return decision, explanation, parsed_response

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LLMDecisionValidationTester:
    """Test LLM decision making and analysis"""
    
    def __init__(self):
        self.results = []
        self.exchange_engine = None
        self.orchestrator = None
    
    async def setup_llm_environment(self):
        """Setup environment for LLM testing"""
        logger.info("Setting up LLM decision validation environment...")
        
        # Create exchange connection for real market data
        self.exchange_engine = CCXTTradingEngine('htx', demo_mode=True)
        if not self.exchange_engine.initialize_exchange():
            raise Exception("Failed to connect to exchange for LLM testing")
        
        # Create LLM orchestrator
        try:
            self.orchestrator = LLMPromptOrchestrator()
            logger.info("✅ LLM orchestrator initialized")
        except Exception as e:
            logger.warning(f"LLM orchestrator initialization failed: {e}")
            self.orchestrator = None
        
        logger.info("✅ LLM decision validation environment setup complete")
    
    async def test_market_analysis_with_real_data(self):
        """Test LLM market analysis with real market data"""
        logger.info("🔧 Testing LLM Market Analysis with Real Data")
        
        symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'DOGE/USDT:USDT']
        analysis_results = []
        
        for symbol in symbols:
            try:
                logger.info(f"Analyzing {symbol}...")
                
                # Get real market data
                ticker = self.exchange_engine.exchange.fetch_ticker(symbol)
                ohlcv = self.exchange_engine.exchange.fetch_ohlcv(symbol, '1m', limit=100)
                
                # Run trading system analysis
                start_time = time.time()
                decision, explanation, parsed_response = run_trading_system(
                    symbol,
                    interval='1m',
                    period=100,
                    use_exchange=True,
                    use_live_data=False
                )
                analysis_time = time.time() - start_time
                
                # Validate decision structure
                valid_decisions = ['LONG', 'SHORT', 'WAIT']
                decision_valid = decision in valid_decisions
                
                # Check if explanation exists and is meaningful
                explanation_valid = explanation and len(explanation) > 50
                
                # Check confidence scoring if available
                confidence_valid = True
                if parsed_response and 'confidence' in parsed_response:
                    confidence = parsed_response['confidence']
                    confidence_valid = 0.0 <= confidence <= 1.0
                
                analysis_result = {
                    'symbol': symbol,
                    'decision': decision,
                    'explanation_length': len(explanation) if explanation else 0,
                    'analysis_time_seconds': analysis_time,
                    'decision_valid': decision_valid,
                    'explanation_valid': explanation_valid,
                    'confidence_valid': confidence_valid,
                    'current_price': ticker['last'],
                    'success': decision_valid and explanation_valid and confidence_valid and analysis_time < 30.0
                }
                
                analysis_results.append(analysis_result)
                logger.info(f"✅ {symbol}: {decision} decision in {analysis_time:.2f}s")
                
            except Exception as e:
                logger.error(f"❌ Analysis failed for {symbol}: {e}")
                analysis_results.append({
                    'symbol': symbol,
                    'error': str(e),
                    'success': False
                })
        
        # Overall test result
        successful_analyses = sum(1 for r in analysis_results if r.get('success', False))
        total_analyses = len(analysis_results)
        
        result = {
            'test': 'LLM Market Analysis with Real Data',
            'symbols_tested': total_analyses,
            'successful_analyses': successful_analyses,
            'success_rate': successful_analyses / total_analyses if total_analyses > 0 else 0,
            'analysis_results': analysis_results,
            'success': successful_analyses >= 2  # At least 2 out of 3 symbols should work
        }
        
        self.results.append(result)
        return result['success']
    
    async def test_decision_consistency(self, iterations=5):
        """Test LLM decision consistency with same market conditions"""
        logger.info("🔧 Testing LLM Decision Consistency")
        
        symbol = 'BTC/USDT:USDT'
        decisions = []
        
        # Get current market data once
        ticker = self.exchange_engine.exchange.fetch_ticker(symbol)
        current_price = ticker['last']
        
        for i in range(iterations):
            try:
                logger.info(f"Decision iteration {i+1}/{iterations}...")
                
                # Run trading system multiple times with same data
                decision, explanation, parsed_response = run_trading_system(
                    symbol,
                    interval='1m',
                    period=50,
                    use_exchange=True,
                    use_live_data=False
                )
                
                decisions.append({
                    'iteration': i + 1,
                    'decision': decision,
                    'explanation_length': len(explanation) if explanation else 0,
                    'confidence': parsed_response.get('confidence', 0.0) if parsed_response else 0.0
                })
                
                logger.info(f"Iteration {i+1}: {decision}")
                
            except Exception as e:
                logger.error(f"❌ Decision iteration {i+1} failed: {e}")
                decisions.append({
                    'iteration': i + 1,
                    'error': str(e)
                })
        
        # Analyze consistency
        valid_decisions = [d for d in decisions if 'decision' in d]
        if valid_decisions:
            decision_types = [d['decision'] for d in valid_decisions]
            most_common_decision = max(set(decision_types), key=decision_types.count)
            consistency_rate = decision_types.count(most_common_decision) / len(decision_types)
        else:
            consistency_rate = 0.0
            most_common_decision = None
        
        result = {
            'test': 'LLM Decision Consistency',
            'iterations': iterations,
            'valid_decisions': len(valid_decisions),
            'most_common_decision': most_common_decision,
            'consistency_rate': consistency_rate,
            'current_price': current_price,
            'decisions': decisions,
            'success': consistency_rate >= 0.6 and len(valid_decisions) >= 3  # 60% consistency, at least 3 valid
        }
        
        self.results.append(result)
        logger.info(f"✅ Decision consistency: {consistency_rate:.1%} ({most_common_decision})")
        return result['success']
    
    async def test_confidence_scoring_accuracy(self):
        """Test LLM confidence scoring accuracy"""
        logger.info("🔧 Testing LLM Confidence Scoring Accuracy")
        
        symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT']
        confidence_tests = []
        
        for symbol in symbols:
            try:
                logger.info(f"Testing confidence scoring for {symbol}...")
                
                # Run analysis
                decision, explanation, parsed_response = run_trading_system(
                    symbol,
                    interval='1m',
                    period=100,
                    use_exchange=True,
                    use_live_data=False
                )
                
                # Extract confidence if available
                confidence = None
                if parsed_response and 'confidence' in parsed_response:
                    confidence = parsed_response['confidence']
                
                # Validate confidence scoring
                confidence_valid = confidence is not None and 0.0 <= confidence <= 1.0
                
                # Check if confidence aligns with decision strength
                decision_strength_valid = True
                if confidence is not None:
                    if decision == 'WAIT':
                        # WAIT decisions should generally have lower confidence
                        decision_strength_valid = confidence <= 0.8
                    else:
                        # LONG/SHORT decisions should have reasonable confidence
                        decision_strength_valid = confidence >= 0.3
                
                confidence_test = {
                    'symbol': symbol,
                    'decision': decision,
                    'confidence': confidence,
                    'confidence_valid': confidence_valid,
                    'decision_strength_valid': decision_strength_valid,
                    'success': confidence_valid and decision_strength_valid
                }
                
                confidence_tests.append(confidence_test)
                logger.info(f"✅ {symbol}: {decision} (confidence: {confidence})")
                
            except Exception as e:
                logger.error(f"❌ Confidence test failed for {symbol}: {e}")
                confidence_tests.append({
                    'symbol': symbol,
                    'error': str(e),
                    'success': False
                })
        
        # Overall confidence scoring result
        successful_tests = sum(1 for t in confidence_tests if t.get('success', False))
        total_tests = len(confidence_tests)
        
        result = {
            'test': 'LLM Confidence Scoring Accuracy',
            'symbols_tested': total_tests,
            'successful_tests': successful_tests,
            'confidence_tests': confidence_tests,
            'success': successful_tests >= 1  # At least 1 symbol should work
        }
        
        self.results.append(result)
        return result['success']
    
    async def test_response_time_performance(self, iterations=10):
        """Test LLM response time performance"""
        logger.info("🔧 Testing LLM Response Time Performance")
        
        symbol = 'BTC/USDT:USDT'
        response_times = []
        
        for i in range(iterations):
            try:
                logger.info(f"Response time test {i+1}/{iterations}...")
                
                start_time = time.time()
                decision, explanation, parsed_response = run_trading_system(
                    symbol,
                    interval='1m',
                    period=50,
                    use_exchange=True,
                    use_live_data=False
                )
                end_time = time.time()
                
                response_time = end_time - start_time
                response_times.append(response_time)
                
                logger.info(f"Response time {i+1}: {response_time:.2f}s")
                
            except Exception as e:
                logger.error(f"❌ Response time test {i+1} failed: {e}")
                continue
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
        else:
            avg_response_time = max_response_time = min_response_time = 0.0
        
        result = {
            'test': 'LLM Response Time Performance',
            'iterations': iterations,
            'successful_responses': len(response_times),
            'avg_response_time_seconds': avg_response_time,
            'max_response_time_seconds': max_response_time,
            'min_response_time_seconds': min_response_time,
            'response_times': response_times,
            'success': avg_response_time < 20.0 and len(response_times) >= 7  # <20s avg, 70% success rate
        }
        
        self.results.append(result)
        logger.info(f"✅ LLM response time: {avg_response_time:.2f}s avg")
        return result['success']
    
    def generate_llm_validation_report(self):
        """Generate comprehensive LLM validation report"""
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'total_tests': len(self.results),
            'passed_tests': sum(1 for r in self.results if r['success']),
            'failed_tests': sum(1 for r in self.results if not r['success']),
            'success_rate': sum(1 for r in self.results if r['success']) / len(self.results) * 100 if self.results else 0,
            'llm_validation_tests': self.results
        }
        
        # Save report
        filename = f"llm_decision_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        return report, filename

async def main():
    """Run all LLM decision making validation tests"""
    print("🚀 Starting LLM Decision Making Validation")
    print("=" * 60)
    
    tester = LLMDecisionValidationTester()
    
    # Setup LLM testing environment
    try:
        await tester.setup_llm_environment()
    except Exception as e:
        print(f"❌ Failed to setup LLM environment: {e}")
        return False
    
    # Run all LLM validation tests
    tests = [
        tester.test_market_analysis_with_real_data,
        lambda: tester.test_decision_consistency(5),
        tester.test_confidence_scoring_accuracy,
        lambda: tester.test_response_time_performance(10),
    ]
    
    results = []
    for i, test in enumerate(tests, 1):
        print(f"\n🔧 Running LLM test {i}/{len(tests)}...")
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ LLM test {i} failed with exception: {e}")
            results.append(False)
    
    # Generate report
    report, filename = tester.generate_llm_validation_report()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 LLM DECISION MAKING VALIDATION SUMMARY")
    print("=" * 60)
    
    for i, (test_result, success) in enumerate(zip(tester.results, results), 1):
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - Test {i}: {test_result['test']}")
    
    print(f"\nResults: {report['passed_tests']}/{report['total_tests']} tests passed")
    print(f"Success Rate: {report['success_rate']:.1f}%")
    print(f"📄 Detailed report saved to: {filename}")
    
    if report['success_rate'] >= 75:
        print("🎉 LLM decision making validation PASSED!")
        return True
    else:
        print("⚠️ Some LLM validation tests failed. Review the detailed report.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
