#!/usr/bin/env python3
"""
Live Data Integration Testing
Comprehensive testing of real exchange APIs with live market data feeds
"""

import sys
import os
import asyncio
import json
import time
import statistics
from datetime import datetime, timedelta
import logging

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from trading.ccxt_trading_engine import CCXTTradingEngine
from data.websocket_client import WebSocketClient

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LiveDataIntegrationTester:
    """Comprehensive live data integration testing"""
    
    def __init__(self):
        self.results = []
        self.exchange_engine = None
        self.websocket_client = None
        self.test_symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'DOGE/USDT:USDT']
    
    async def setup_live_connections(self):
        """Setup live exchange connections"""
        logger.info("🔗 Setting up live exchange connections...")
        
        # Setup HTX exchange connection
        self.exchange_engine = CCXTTradingEngine('htx', demo_mode=True)
        if not self.exchange_engine.initialize_exchange():
            raise Exception("Failed to connect to HTX exchange")
        
        logger.info("✅ HTX exchange connection established")
        
        # Test exchange info
        try:
            markets = self.exchange_engine.exchange.load_markets()
            logger.info(f"✅ Loaded {len(markets)} markets from HTX")
        except Exception as e:
            logger.warning(f"⚠️ Could not load markets: {e}")
        
        logger.info("✅ Live data integration environment ready")
    
    async def test_real_time_price_feeds(self, duration_seconds=60):
        """Test real-time price feed consistency and accuracy"""
        logger.info(f"🔧 Testing Real-Time Price Feeds ({duration_seconds}s)")
        
        price_updates = {symbol: [] for symbol in self.test_symbols}
        start_time = time.time()
        
        while time.time() - start_time < duration_seconds:
            for symbol in self.test_symbols:
                try:
                    ticker = self.exchange_engine.exchange.fetch_ticker(symbol)
                    price_updates[symbol].append({
                        'timestamp': time.time(),
                        'price': ticker['last'],
                        'bid': ticker['bid'],
                        'ask': ticker['ask'],
                        'volume': ticker['quoteVolume']
                    })
                    
                except Exception as e:
                    logger.warning(f"Price fetch error for {symbol}: {e}")
            
            await asyncio.sleep(2)  # 2-second intervals
        
        # Analyze price feed quality
        feed_quality = {}
        for symbol, updates in price_updates.items():
            if len(updates) >= 10:  # Minimum updates for analysis
                prices = [u['price'] for u in updates]
                timestamps = [u['timestamp'] for u in updates]
                
                # Calculate update frequency
                intervals = [timestamps[i] - timestamps[i-1] for i in range(1, len(timestamps))]
                avg_interval = statistics.mean(intervals) if intervals else 0
                
                # Calculate price volatility
                price_changes = [abs(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
                avg_volatility = statistics.mean(price_changes) if price_changes else 0
                
                feed_quality[symbol] = {
                    'total_updates': len(updates),
                    'avg_interval_seconds': avg_interval,
                    'avg_volatility_percent': avg_volatility * 100,
                    'price_range': (min(prices), max(prices)),
                    'data_quality': 'good' if len(updates) >= 20 and avg_interval < 5 else 'acceptable'
                }
                
                logger.info(f"✅ {symbol}: {len(updates)} updates, {avg_interval:.1f}s intervals")
            else:
                feed_quality[symbol] = {'error': 'insufficient_data'}
        
        result = {
            'test': 'Real-Time Price Feeds',
            'duration_seconds': duration_seconds,
            'symbols_tested': len(self.test_symbols),
            'feed_quality': feed_quality,
            'success': all(q.get('data_quality') in ['good', 'acceptable'] for q in feed_quality.values())
        }
        
        self.results.append(result)
        return result['success']
    
    async def test_orderbook_data_quality(self):
        """Test orderbook data depth and quality"""
        logger.info("🔧 Testing Orderbook Data Quality")
        
        orderbook_tests = {}
        
        for symbol in self.test_symbols:
            try:
                logger.info(f"Testing orderbook for {symbol}...")
                
                # Fetch deep orderbook
                orderbook = self.exchange_engine.exchange.fetch_order_book(symbol, limit=100)
                
                bids = orderbook['bids']
                asks = orderbook['asks']
                
                # Analyze orderbook quality
                bid_depth = sum(bid[1] for bid in bids[:10])  # Top 10 levels
                ask_depth = sum(ask[1] for ask in asks[:10])
                
                spread = asks[0][0] - bids[0][0] if bids and asks else 0
                spread_pct = (spread / bids[0][0] * 100) if bids and spread > 0 else 0
                
                orderbook_tests[symbol] = {
                    'bid_levels': len(bids),
                    'ask_levels': len(asks),
                    'bid_depth': bid_depth,
                    'ask_depth': ask_depth,
                    'spread_usd': spread,
                    'spread_percent': spread_pct,
                    'best_bid': bids[0][0] if bids else 0,
                    'best_ask': asks[0][0] if asks else 0,
                    'quality': 'excellent' if len(bids) >= 50 and len(asks) >= 50 else 'good'
                }
                
                logger.info(f"✅ {symbol}: {len(bids)} bids, {len(asks)} asks, {spread_pct:.3f}% spread")
                
            except Exception as e:
                logger.error(f"❌ Orderbook test failed for {symbol}: {e}")
                orderbook_tests[symbol] = {'error': str(e)}
        
        successful_tests = sum(1 for test in orderbook_tests.values() if 'quality' in test)
        
        result = {
            'test': 'Orderbook Data Quality',
            'symbols_tested': len(self.test_symbols),
            'successful_tests': successful_tests,
            'orderbook_analysis': orderbook_tests,
            'success': successful_tests >= 2  # At least 2 symbols should work
        }
        
        self.results.append(result)
        return result['success']
    
    async def test_historical_data_accuracy(self):
        """Test historical data accuracy and completeness"""
        logger.info("🔧 Testing Historical Data Accuracy")
        
        historical_tests = {}
        timeframes = ['1m', '5m', '1h']
        
        for symbol in self.test_symbols[:2]:  # Test first 2 symbols to save time
            historical_tests[symbol] = {}
            
            for timeframe in timeframes:
                try:
                    logger.info(f"Testing {symbol} {timeframe} historical data...")
                    
                    # Fetch recent historical data
                    ohlcv = self.exchange_engine.exchange.fetch_ohlcv(symbol, timeframe, limit=100)
                    
                    if ohlcv and len(ohlcv) >= 50:
                        # Analyze data quality
                        timestamps = [candle[0] for candle in ohlcv]
                        opens = [candle[1] for candle in ohlcv]
                        highs = [candle[2] for candle in ohlcv]
                        lows = [candle[3] for candle in ohlcv]
                        closes = [candle[4] for candle in ohlcv]
                        volumes = [candle[5] for candle in ohlcv]
                        
                        # Check data consistency
                        valid_candles = sum(1 for i in range(len(ohlcv)) 
                                          if lows[i] <= opens[i] <= highs[i] and 
                                             lows[i] <= closes[i] <= highs[i])
                        
                        data_quality = valid_candles / len(ohlcv)
                        
                        # Check timestamp consistency
                        time_intervals = [timestamps[i] - timestamps[i-1] for i in range(1, len(timestamps))]
                        consistent_intervals = len(set(time_intervals)) <= 2  # Allow some variation
                        
                        historical_tests[symbol][timeframe] = {
                            'candles_count': len(ohlcv),
                            'data_quality_percent': data_quality * 100,
                            'consistent_intervals': consistent_intervals,
                            'avg_volume': statistics.mean(volumes) if volumes else 0,
                            'latest_timestamp': timestamps[-1] if timestamps else 0,
                            'success': data_quality >= 0.95 and consistent_intervals
                        }
                        
                        logger.info(f"✅ {symbol} {timeframe}: {len(ohlcv)} candles, {data_quality:.1%} quality")
                    else:
                        historical_tests[symbol][timeframe] = {'error': 'insufficient_data'}
                        
                except Exception as e:
                    logger.error(f"❌ Historical data test failed for {symbol} {timeframe}: {e}")
                    historical_tests[symbol][timeframe] = {'error': str(e)}
        
        # Count successful tests
        successful_tests = 0
        total_tests = 0
        for symbol_tests in historical_tests.values():
            for timeframe_test in symbol_tests.values():
                total_tests += 1
                if timeframe_test.get('success', False):
                    successful_tests += 1
        
        result = {
            'test': 'Historical Data Accuracy',
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': successful_tests / total_tests if total_tests > 0 else 0,
            'historical_analysis': historical_tests,
            'success': successful_tests >= total_tests * 0.8  # 80% success rate
        }
        
        self.results.append(result)
        return result['success']
    
    async def test_data_pipeline_performance(self, iterations=50):
        """Test data pipeline performance under load"""
        logger.info(f"🔧 Testing Data Pipeline Performance ({iterations} iterations)")
        
        performance_metrics = {
            'fetch_times': [],
            'data_sizes': [],
            'error_count': 0
        }
        
        for i in range(iterations):
            start_time = time.time()
            
            try:
                # Fetch multiple data types simultaneously
                tasks = []
                for symbol in self.test_symbols:
                    tasks.append(self.fetch_symbol_data(symbol))
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                end_time = time.time()
                fetch_time = end_time - start_time
                
                # Count successful fetches
                successful_fetches = sum(1 for r in results if not isinstance(r, Exception))
                data_size = successful_fetches * 3  # Approximate data points
                
                performance_metrics['fetch_times'].append(fetch_time)
                performance_metrics['data_sizes'].append(data_size)
                
                if successful_fetches < len(self.test_symbols):
                    performance_metrics['error_count'] += 1
                
                if i % 10 == 0:
                    logger.info(f"Iteration {i+1}/{iterations}: {fetch_time:.2f}s, {successful_fetches} successful")
                
            except Exception as e:
                performance_metrics['error_count'] += 1
                logger.warning(f"Performance test iteration {i+1} failed: {e}")
        
        # Calculate performance statistics
        if performance_metrics['fetch_times']:
            avg_fetch_time = statistics.mean(performance_metrics['fetch_times'])
            max_fetch_time = max(performance_metrics['fetch_times'])
            p95_fetch_time = statistics.quantiles(performance_metrics['fetch_times'], n=20)[18]
            
            throughput = sum(performance_metrics['data_sizes']) / sum(performance_metrics['fetch_times'])
            error_rate = performance_metrics['error_count'] / iterations
        else:
            avg_fetch_time = max_fetch_time = p95_fetch_time = throughput = 0
            error_rate = 1.0
        
        result = {
            'test': 'Data Pipeline Performance',
            'iterations': iterations,
            'avg_fetch_time_seconds': avg_fetch_time,
            'max_fetch_time_seconds': max_fetch_time,
            'p95_fetch_time_seconds': p95_fetch_time,
            'throughput_data_points_per_second': throughput,
            'error_rate': error_rate,
            'success': avg_fetch_time < 2.0 and error_rate < 0.1  # <2s avg, <10% errors
        }
        
        self.results.append(result)
        logger.info(f"✅ Pipeline performance: {avg_fetch_time:.2f}s avg, {error_rate:.1%} errors")
        return result['success']
    
    async def fetch_symbol_data(self, symbol):
        """Helper function to fetch data for a single symbol"""
        ticker = self.exchange_engine.exchange.fetch_ticker(symbol)
        orderbook = self.exchange_engine.exchange.fetch_order_book(symbol, limit=10)
        ohlcv = self.exchange_engine.exchange.fetch_ohlcv(symbol, '1m', limit=10)
        return {'ticker': ticker, 'orderbook': orderbook, 'ohlcv': ohlcv}
    
    def generate_live_data_report(self):
        """Generate comprehensive live data integration report"""
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'total_tests': len(self.results),
            'passed_tests': sum(1 for r in self.results if r['success']),
            'failed_tests': sum(1 for r in self.results if not r['success']),
            'success_rate': sum(1 for r in self.results if r['success']) / len(self.results) * 100 if self.results else 0,
            'live_data_tests': self.results
        }
        
        # Save report
        filename = f"live_data_integration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        return report, filename

async def main():
    """Run all live data integration tests"""
    print("🚀 Starting Live Data Integration Testing")
    print("=" * 60)
    
    tester = LiveDataIntegrationTester()
    
    # Setup live connections
    try:
        await tester.setup_live_connections()
    except Exception as e:
        print(f"❌ Failed to setup live connections: {e}")
        return False
    
    # Run all live data tests
    tests = [
        lambda: tester.test_real_time_price_feeds(60),
        tester.test_orderbook_data_quality,
        tester.test_historical_data_accuracy,
        lambda: tester.test_data_pipeline_performance(50),
    ]
    
    results = []
    for i, test in enumerate(tests, 1):
        print(f"\n🔧 Running live data test {i}/{len(tests)}...")
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Live data test {i} failed with exception: {e}")
            results.append(False)
    
    # Generate report
    report, filename = tester.generate_live_data_report()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 LIVE DATA INTEGRATION TESTING SUMMARY")
    print("=" * 60)
    
    for i, (test_result, success) in enumerate(zip(tester.results, results), 1):
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - Test {i}: {test_result['test']}")
    
    print(f"\nResults: {report['passed_tests']}/{report['total_tests']} tests passed")
    print(f"Success Rate: {report['success_rate']:.1f}%")
    print(f"📄 Detailed report saved to: {filename}")
    
    if report['success_rate'] >= 75:
        print("🎉 Live data integration testing PASSED!")
        return True
    else:
        print("⚠️ Some live data tests failed. Review the detailed report.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
