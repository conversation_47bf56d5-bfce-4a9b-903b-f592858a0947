#!/usr/bin/env python3
"""
Performance and Latency Testing
Measures system performance under real market conditions and validates execution speed
"""

import sys
import os
import asyncio
import json
import time
import statistics
from datetime import datetime
import logging
import concurrent.futures

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from trading.ccxt_trading_engine import CCXTTradingEngine
from portfolio.portfolio_manager import PortfolioManager
from execution.autonomous_executor import AutonomousTradeExecutor
from core.llm_orchestrator import LLMPromptOrchestrator
from tests.mocks.mock_exchange import MockExchange

# Setup logging
logging.basicConfig(level=logging.WARNING)  # Reduce noise for performance testing
logger = logging.getLogger(__name__)

class PerformanceLatencyTester:
    """Test system performance and latency"""
    
    def __init__(self):
        self.results = []
        self.exchange_engine = None
        self.portfolio = None
        self.executor = None
        self.orchestrator = None
    
    async def setup_performance_environment(self):
        """Setup environment for performance testing"""
        print("Setting up performance testing environment...")
        
        # Create exchange connection (demo mode for safety)
        self.exchange_engine = CCXTTradingEngine('htx', demo_mode=True)
        if not self.exchange_engine.initialize_exchange():
            raise Exception("Failed to connect to exchange for performance testing")
        
        # Create portfolio manager
        self.portfolio = PortfolioManager(initial_balance=10000.0, max_positions=5)
        
        # Create autonomous executor
        self.executor = AutonomousTradeExecutor(self.exchange_engine.exchange)
        
        print("✅ Performance testing environment setup complete")
    
    async def test_market_data_latency(self, iterations=50):
        """Test market data fetching latency"""
        print("🔧 Testing Market Data Latency")
        
        symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'DOGE/USDT:USDT']
        latencies = []
        
        for i in range(iterations):
            start_time = time.time()
            
            try:
                # Fetch ticker data
                ticker = self.exchange_engine.exchange.fetch_ticker('BTC/USDT:USDT')
                end_time = time.time()
                
                latency = (end_time - start_time) * 1000  # Convert to milliseconds
                latencies.append(latency)
                
                if i % 10 == 0:
                    print(f"  Iteration {i+1}/{iterations}: {latency:.2f}ms")
                    
            except Exception as e:
                print(f"  Error in iteration {i+1}: {e}")
                continue
        
        if latencies:
            avg_latency = statistics.mean(latencies)
            min_latency = min(latencies)
            max_latency = max(latencies)
            p95_latency = statistics.quantiles(latencies, n=20)[18]  # 95th percentile
            
            result = {
                'test': 'Market Data Latency',
                'iterations': len(latencies),
                'avg_latency_ms': avg_latency,
                'min_latency_ms': min_latency,
                'max_latency_ms': max_latency,
                'p95_latency_ms': p95_latency,
                'success': avg_latency < 500.0 and p95_latency < 1000.0  # Target: <500ms avg, <1s p95
            }
            
            self.results.append(result)
            print(f"✅ Market data latency: {avg_latency:.2f}ms avg, {p95_latency:.2f}ms p95")
            return result['success']
        else:
            print("❌ No successful market data requests")
            return False
    
    async def test_portfolio_calculation_performance(self, iterations=1000):
        """Test portfolio calculation performance"""
        print("🔧 Testing Portfolio Calculation Performance")
        
        # Setup test positions
        await self.portfolio.open_position('BTC/USDT', 'long', 0.01, 50000.0, 1.0)
        await self.portfolio.open_position('ETH/USDT', 'short', 0.5, 3000.0, 1.0)
        
        calculation_times = []
        
        for i in range(iterations):
            start_time = time.time()
            
            # Perform portfolio calculations
            total_value = self.portfolio.get_total_value()
            total_risk = self.portfolio.calculate_total_portfolio_risk()
            summary = self.portfolio.get_portfolio_summary()
            unrealized_pnl = summary['unrealized_pnl']
            
            end_time = time.time()
            
            calculation_time = (end_time - start_time) * 1000  # Convert to milliseconds
            calculation_times.append(calculation_time)
            
            if i % 200 == 0:
                print(f"  Iteration {i+1}/{iterations}: {calculation_time:.3f}ms")
        
        avg_calc_time = statistics.mean(calculation_times)
        max_calc_time = max(calculation_times)
        p95_calc_time = statistics.quantiles(calculation_times, n=20)[18]
        
        result = {
            'test': 'Portfolio Calculation Performance',
            'iterations': iterations,
            'avg_calculation_time_ms': avg_calc_time,
            'max_calculation_time_ms': max_calc_time,
            'p95_calculation_time_ms': p95_calc_time,
            'calculations_per_second': 1000 / avg_calc_time,
            'success': avg_calc_time < 1.0 and p95_calc_time < 5.0  # Target: <1ms avg, <5ms p95
        }
        
        self.results.append(result)
        print(f"✅ Portfolio calculations: {avg_calc_time:.3f}ms avg, {1000/avg_calc_time:.0f} calc/s")
        return result['success']
    
    async def test_concurrent_operations(self, concurrent_requests=10):
        """Test system performance under concurrent load"""
        print("🔧 Testing Concurrent Operations Performance")
        
        async def concurrent_portfolio_operation():
            """Single concurrent operation"""
            start_time = time.time()
            
            # Simulate concurrent portfolio operations
            total_value = self.portfolio.get_total_value()
            total_risk = self.portfolio.calculate_total_portfolio_risk()
            
            end_time = time.time()
            return (end_time - start_time) * 1000
        
        # Run concurrent operations
        start_time = time.time()
        
        tasks = [concurrent_portfolio_operation() for _ in range(concurrent_requests)]
        operation_times = await asyncio.gather(*tasks)
        
        total_time = (time.time() - start_time) * 1000
        
        avg_operation_time = statistics.mean(operation_times)
        max_operation_time = max(operation_times)
        throughput = concurrent_requests / (total_time / 1000)
        
        result = {
            'test': 'Concurrent Operations Performance',
            'concurrent_requests': concurrent_requests,
            'total_time_ms': total_time,
            'avg_operation_time_ms': avg_operation_time,
            'max_operation_time_ms': max_operation_time,
            'throughput_ops_per_second': throughput,
            'success': avg_operation_time < 10.0 and throughput > 50.0  # Target: <10ms avg, >50 ops/s
        }
        
        self.results.append(result)
        print(f"✅ Concurrent operations: {throughput:.1f} ops/s, {avg_operation_time:.2f}ms avg")
        return result['success']
    
    async def test_memory_usage_efficiency(self):
        """Test memory usage efficiency"""
        print("🔧 Testing Memory Usage Efficiency")
        
        import psutil
        import gc
        
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform memory-intensive operations
        large_operations = []
        for i in range(100):
            # Create and process large datasets
            await self.portfolio.update_positions({
                'BTC/USDT': 50000.0 + (i * 10),
                'ETH/USDT': 3000.0 + (i * 5)
            })
            
            # Store operation results
            large_operations.append({
                'iteration': i,
                'portfolio_value': self.portfolio.get_total_value(),
                'portfolio_risk': self.portfolio.calculate_total_portfolio_risk()
            })
        
        # Get peak memory usage
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Force garbage collection
        gc.collect()
        
        # Get final memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        memory_increase = peak_memory - initial_memory
        memory_efficiency = (final_memory - initial_memory) / memory_increase
        
        result = {
            'test': 'Memory Usage Efficiency',
            'initial_memory_mb': initial_memory,
            'peak_memory_mb': peak_memory,
            'final_memory_mb': final_memory,
            'memory_increase_mb': memory_increase,
            'memory_efficiency_ratio': memory_efficiency,
            'success': memory_increase < 100.0 and memory_efficiency < 0.5  # Target: <100MB increase, <50% retained
        }
        
        self.results.append(result)
        print(f"✅ Memory usage: {memory_increase:.1f}MB increase, {memory_efficiency:.2f} efficiency")
        return result['success']
    
    async def test_order_execution_simulation(self, orders=100):
        """Test simulated order execution performance"""
        print("🔧 Testing Order Execution Simulation Performance")
        
        # Use mock exchange for consistent performance testing
        mock_exchange = MockExchange(initial_balance=10000.0)
        execution_times = []
        
        for i in range(orders):
            start_time = time.time()
            
            # Simulate order execution
            try:
                # Check if position can be opened
                can_open = await self.portfolio.can_open_position('BTC/USDT', 100.0, 1.0)
                
                if can_open['allowed']:
                    # Simulate order placement
                    order_result = {
                        'symbol': 'BTC/USDT',
                        'side': 'buy',
                        'amount': 0.001,
                        'price': 50000.0,
                        'status': 'filled'
                    }
                
                end_time = time.time()
                execution_time = (end_time - start_time) * 1000
                execution_times.append(execution_time)
                
                if i % 20 == 0:
                    print(f"  Order {i+1}/{orders}: {execution_time:.2f}ms")
                    
            except Exception as e:
                print(f"  Error in order {i+1}: {e}")
                continue
        
        if execution_times:
            avg_execution_time = statistics.mean(execution_times)
            max_execution_time = max(execution_times)
            p95_execution_time = statistics.quantiles(execution_times, n=20)[18]
            orders_per_second = 1000 / avg_execution_time
            
            result = {
                'test': 'Order Execution Simulation Performance',
                'orders_processed': len(execution_times),
                'avg_execution_time_ms': avg_execution_time,
                'max_execution_time_ms': max_execution_time,
                'p95_execution_time_ms': p95_execution_time,
                'orders_per_second': orders_per_second,
                'success': avg_execution_time < 50.0 and orders_per_second > 20.0  # Target: <50ms avg, >20 orders/s
            }
            
            self.results.append(result)
            print(f"✅ Order execution: {avg_execution_time:.2f}ms avg, {orders_per_second:.1f} orders/s")
            return result['success']
        else:
            print("❌ No successful order executions")
            return False
    
    def generate_performance_report(self):
        """Generate comprehensive performance testing report"""
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'total_tests': len(self.results),
            'passed_tests': sum(1 for r in self.results if r['success']),
            'failed_tests': sum(1 for r in self.results if not r['success']),
            'success_rate': sum(1 for r in self.results if r['success']) / len(self.results) * 100 if self.results else 0,
            'performance_tests': self.results
        }
        
        # Save report
        filename = f"performance_latency_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        return report, filename

async def main():
    """Run all performance and latency tests"""
    print("🚀 Starting Performance and Latency Testing")
    print("=" * 60)
    
    tester = PerformanceLatencyTester()
    
    # Setup performance testing environment
    try:
        await tester.setup_performance_environment()
    except Exception as e:
        print(f"❌ Failed to setup performance environment: {e}")
        return False
    
    # Run all performance tests
    tests = [
        lambda: tester.test_market_data_latency(50),
        lambda: tester.test_portfolio_calculation_performance(1000),
        lambda: tester.test_concurrent_operations(10),
        lambda: tester.test_memory_usage_efficiency(),
        lambda: tester.test_order_execution_simulation(100),
    ]
    
    results = []
    for i, test in enumerate(tests, 1):
        print(f"\n🔧 Running performance test {i}/{len(tests)}...")
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Performance test {i} failed with exception: {e}")
            results.append(False)
    
    # Generate report
    report, filename = tester.generate_performance_report()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 PERFORMANCE AND LATENCY TESTING SUMMARY")
    print("=" * 60)
    
    for i, (test_result, success) in enumerate(zip(tester.results, results), 1):
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - Test {i}: {test_result['test']}")
    
    print(f"\nResults: {report['passed_tests']}/{report['total_tests']} tests passed")
    print(f"Success Rate: {report['success_rate']:.1f}%")
    print(f"📄 Detailed report saved to: {filename}")
    
    if report['success_rate'] >= 80:
        print("🎉 Performance and latency testing PASSED!")
        return True
    else:
        print("⚠️ Some performance tests failed. Review the detailed report.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
