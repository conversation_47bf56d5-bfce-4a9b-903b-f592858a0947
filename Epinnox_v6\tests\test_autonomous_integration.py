"""
Integration tests for EPINNOX v6 autonomous trading system
Tests the interaction between different autonomous components
"""

import pytest
import asyncio
import pandas as pd
from unittest.mock import Mock, patch
from datetime import datetime

from tests.mocks.mock_exchange import MockExchange
from execution.autonomous_executor import AutonomousTradeExecutor
from portfolio.portfolio_manager import PortfolioManager
from monitoring.performance_tracker import PerformanceTracker
from core.autonomous_controller import AutonomousController

class TestAutonomousSystemIntegration:
    
    @pytest.fixture
    def mock_exchange(self):
        """Create mock exchange for integration testing"""
        return MockExchange(initial_balance=10000.0)
    
    @pytest.fixture
    def autonomous_executor(self, mock_exchange):
        """Create autonomous executor with mock exchange"""
        return AutonomousTradeExecutor(
            exchange=mock_exchange,
            risk_manager=Mock(),
            min_confidence=0.65
        )
    
    @pytest.fixture
    def portfolio_manager(self):
        """Create portfolio manager for testing"""
        return PortfolioManager(initial_balance=10000.0, max_positions=5)
    
    @pytest.fixture
    def performance_tracker(self, temp_db_path):
        """Create performance tracker for testing"""
        return PerformanceTracker(db_path=temp_db_path)
    
    @pytest.mark.asyncio
    async def test_full_trading_cycle(self, autonomous_executor, portfolio_manager, performance_tracker):
        """Test complete trading cycle: decision -> execution -> portfolio -> tracking"""
        
        # 1. Create trading decision
        decision_data = {
            'decision': 'LONG',
            'confidence': 75,
            'selected_symbol': 'BTC/USDT',
            'leverage_position_sizing': {
                'position_units': 0.1,
                'position_usd': 5000.0,
                'effective_leverage': 2.0,
                'stop_loss_price': 49000.0,
                'take_profit_price': 52000.0
            }
        }
        
        # 2. Execute trade
        execution_result = await autonomous_executor.execute_trading_decision(decision_data)
        assert execution_result['status'] == 'FILLED'
        
        # 3. Update portfolio
        await portfolio_manager.open_position(
            symbol=execution_result['symbol'],
            side=execution_result['side'],
            size=execution_result['amount'],
            entry_price=execution_result['price'],
            leverage=decision_data['leverage_position_sizing']['effective_leverage']
        )
        
        # 4. Record performance
        trade_data = {
            'timestamp': datetime.now(),
            'symbol': execution_result['symbol'],
            'decision': decision_data['decision'],
            'confidence': decision_data['confidence'],
            'entry_price': execution_result['price'],
            'position_size': execution_result['amount'],
            'leverage': decision_data['leverage_position_sizing']['effective_leverage'],
            'execution_status': execution_result['status'],
            'trade_source': 'integration_test'
        }
        performance_tracker.record_trade(trade_data)
        
        # 5. Verify integration
        portfolio_summary = portfolio_manager.get_portfolio_summary()
        assert portfolio_summary['open_positions'] == 1
        assert 'BTC/USDT' in portfolio_summary['positions']
        
        # Check performance tracking
        recent_performance = performance_tracker.get_recent_performance_summary(hours=1)
        assert recent_performance['total_trades'] == 1
    
    @pytest.mark.asyncio
    async def test_risk_management_integration(self, autonomous_executor, portfolio_manager):
        """Test risk management across executor and portfolio manager"""
        
        # Fill portfolio to near capacity
        for i in range(4):
            await portfolio_manager.open_position(
                symbol=f'TEST{i}/USDT',
                side='long',
                size=0.1,
                entry_price=100.0,
                leverage=1.0
            )
        
        # Try to execute trade that would exceed position limits
        decision_data = {
            'decision': 'LONG',
            'confidence': 75,
            'selected_symbol': 'BTC/USDT',
            'leverage_position_sizing': {
                'position_units': 0.1,
                'position_usd': 2000.0,  # Large position
                'effective_leverage': 5.0  # High leverage
            }
        }
        
        # Should be blocked by risk management
        result = await autonomous_executor.execute_trading_decision(decision_data)
        
        # Verify risk management worked
        assert result['status'] in ['RISK_REJECTED', 'SKIPPED']
    
    @pytest.mark.asyncio
    async def test_portfolio_position_updates(self, portfolio_manager, mock_exchange):
        """Test portfolio position updates with market data"""
        
        # Open positions
        await portfolio_manager.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0)
        await portfolio_manager.open_position('ETH/USDT', 'short', 1.0, 3000.0, 1.5)
        
        # Simulate price updates
        mock_exchange.simulate_market_movement('BTC/USDT', 'up', 0.02)  # +2%
        mock_exchange.simulate_market_movement('ETH/USDT', 'down', 0.015)  # -1.5%
        
        price_updates = {
            'BTC/USDT': mock_exchange.get_market_price('BTC/USDT'),
            'ETH/USDT': mock_exchange.get_market_price('ETH/USDT')
        }
        
        # Update positions
        await portfolio_manager.update_positions(price_updates)
        
        # Verify updates
        btc_position = portfolio_manager.positions['BTC/USDT']
        eth_position = portfolio_manager.positions['ETH/USDT']
        
        assert btc_position.unrealized_pnl > 0  # Long position should profit from price increase
        assert eth_position.unrealized_pnl > 0  # Short position should profit from price decrease
    
    @pytest.mark.asyncio
    async def test_stop_loss_integration(self, portfolio_manager, mock_exchange):
        """Test stop loss triggering across portfolio and exchange"""
        
        # Open position with stop loss
        await portfolio_manager.open_position(
            'BTC/USDT', 'long', 0.1, 50000.0, 2.0, 
            stop_loss=49000.0
        )
        
        # Simulate price drop to trigger stop loss
        mock_exchange.simulate_market_movement('BTC/USDT', 'down', 0.025)  # -2.5%
        trigger_price = mock_exchange.get_market_price('BTC/USDT')
        
        # Check stop loss trigger
        await portfolio_manager.check_risk_levels('BTC/USDT', trigger_price)
        
        # Position should be closed
        assert 'BTC/USDT' not in portfolio_manager.positions
        assert len(portfolio_manager.trade_history) == 1
    
    @pytest.mark.asyncio
    async def test_performance_tracking_integration(self, performance_tracker, portfolio_manager):
        """Test performance tracking with actual portfolio trades"""
        
        # Execute several trades
        trades = [
            {'symbol': 'BTC/USDT', 'decision': 'LONG', 'pnl_usd': 100.0, 'confidence': 75.0},
            {'symbol': 'ETH/USDT', 'decision': 'SHORT', 'pnl_usd': -50.0, 'confidence': 80.0},
            {'symbol': 'ADA/USDT', 'decision': 'LONG', 'pnl_usd': 75.0, 'confidence': 70.0}
        ]
        
        for trade in trades:
            trade_data = {
                'timestamp': datetime.now(),
                'symbol': trade['symbol'],
                'decision': trade['decision'],
                'confidence': trade['confidence'],
                'entry_price': 100.0,
                'exit_price': 100.0 + (trade['pnl_usd'] / 10),  # Simplified
                'position_size': 10.0,
                'leverage': 1.0,
                'pnl_usd': trade['pnl_usd'],
                'pnl_pct': trade['pnl_usd'] / 1000,
                'duration_minutes': 30.0,
                'execution_status': 'FILLED',
                'trade_source': 'integration_test'
            }
            performance_tracker.record_trade(trade_data)
        
        # Calculate metrics
        metrics = performance_tracker.calculate_daily_metrics()
        
        assert metrics['total_trades'] == 3
        assert metrics['winning_trades'] == 2
        assert metrics['losing_trades'] == 1
        assert metrics['win_rate'] == 2/3
        assert metrics['total_pnl'] == 125.0
    
    @pytest.mark.asyncio
    async def test_autonomous_controller_initialization(self, mock_exchange, sample_config):
        """Test autonomous controller component integration"""
        
        controller = AutonomousController(mock_exchange, sample_config)
        
        # Initialize all components
        await controller.initialize()
        
        # Verify components are initialized
        assert controller.portfolio_manager is not None
        assert controller.performance_tracker is not None
        assert controller.trade_executor is not None
        
        # Test configuration application
        assert controller.portfolio_manager.initial_balance == sample_config['initial_balance']
        assert controller.portfolio_manager.max_positions == sample_config['max_positions']
    
    @pytest.mark.asyncio
    async def test_decision_to_execution_flow(self, autonomous_executor, mock_exchange):
        """Test the flow from trading decision to actual execution"""
        
        # Test different decision types
        decisions = [
            {
                'decision': 'LONG',
                'confidence': 75,
                'selected_symbol': 'BTC/USDT',
                'leverage_position_sizing': {
                    'position_units': 0.1,
                    'position_usd': 5000.0,
                    'effective_leverage': 2.0
                }
            },
            {
                'decision': 'WAIT',
                'confidence': 45,
                'selected_symbol': 'ETH/USDT'
            },
            {
                'decision': 'SHORT',
                'confidence': 80,
                'selected_symbol': 'ADA/USDT',
                'leverage_position_sizing': {
                    'position_units': 100.0,
                    'position_usd': 50.0,
                    'effective_leverage': 1.5
                }
            }
        ]
        
        results = []
        for decision in decisions:
            result = await autonomous_executor.execute_trading_decision(decision)
            results.append(result)
        
        # Verify results
        assert results[0]['status'] == 'FILLED'  # LONG with high confidence
        assert results[1]['status'] == 'WAIT'    # WAIT decision
        assert results[2]['status'] == 'FILLED'  # SHORT with high confidence
        
        # Verify orders were created in mock exchange
        orders = mock_exchange.get_order_history()
        assert len(orders) == 2  # Two executed trades
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, autonomous_executor):
        """Test error handling across integrated components"""
        
        # Test with malformed decision data
        malformed_decisions = [
            {},  # Empty decision
            {'decision': 'INVALID'},  # Invalid decision
            {'decision': 'LONG', 'confidence': 75},  # Missing position sizing
            {'decision': 'LONG', 'confidence': 'invalid'}  # Invalid confidence type
        ]
        
        for decision in malformed_decisions:
            result = await autonomous_executor.execute_trading_decision(decision)
            assert result['status'] in ['ERROR', 'SKIPPED', 'WAIT']
            assert 'reason' in result
