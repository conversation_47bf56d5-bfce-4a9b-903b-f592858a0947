#!/usr/bin/env python3
"""
AI Model Training with Live Data Testing
Tests RL agent models using real historical and streaming market data
"""

import sys
import os
import asyncio
import json
import time
import numpy as np
from datetime import datetime, timedelta
import logging

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from trading.ccxt_trading_engine import CCXTTradingEngine
from tests.mocks.mock_rl_agent import MockRLAgent
from tests.mocks.mock_trading_environment import MockTradingEnvironment

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AIModelTrainingTester:
    """Test AI model training with live market data"""
    
    def __init__(self):
        self.results = []
        self.exchange_engine = None
        self.rl_agent = None
        self.trading_env = None
        self.test_symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT']
    
    async def setup_ai_training_environment(self):
        """Setup AI training environment with live data"""
        logger.info("🔗 Setting up AI model training environment...")
        
        # Setup exchange connection for live data
        self.exchange_engine = CCXTTradingEngine('htx', demo_mode=True)
        if not self.exchange_engine.initialize_exchange():
            raise Exception("Failed to connect to exchange for AI training")
        
        # Create mock RL agent for testing
        self.rl_agent = MockRLAgent()
        
        # Create mock trading environment
        self.trading_env = MockTradingEnvironment(initial_balance=10000.0)
        
        logger.info("✅ AI model training environment ready")
    
    async def test_live_data_preprocessing(self):
        """Test preprocessing of live market data for AI models"""
        logger.info("🔧 Testing Live Data Preprocessing for AI Models")
        
        preprocessing_results = {}
        
        for symbol in self.test_symbols:
            try:
                logger.info(f"Testing data preprocessing for {symbol}...")
                
                # Fetch live historical data
                ohlcv_1m = self.exchange_engine.exchange.fetch_ohlcv(symbol, '1m', limit=200)
                ohlcv_5m = self.exchange_engine.exchange.fetch_ohlcv(symbol, '5m', limit=100)
                
                # Convert to numpy arrays for AI processing
                prices_1m = np.array([candle[4] for candle in ohlcv_1m])  # Close prices
                volumes_1m = np.array([candle[5] for candle in ohlcv_1m])  # Volumes
                
                prices_5m = np.array([candle[4] for candle in ohlcv_5m])
                volumes_5m = np.array([candle[5] for candle in ohlcv_5m])
                
                # Calculate technical indicators (simplified)
                returns_1m = np.diff(prices_1m) / prices_1m[:-1]
                volatility_1m = np.std(returns_1m[-20:])  # 20-period volatility
                
                # Calculate moving averages
                ma_short = np.mean(prices_1m[-10:])  # 10-period MA
                ma_long = np.mean(prices_1m[-50:])   # 50-period MA
                
                # Normalize data for AI models
                normalized_prices = (prices_1m - np.mean(prices_1m)) / np.std(prices_1m)
                normalized_volumes = (volumes_1m - np.mean(volumes_1m)) / np.std(volumes_1m)
                
                # Create feature vector
                feature_vector = np.array([
                    prices_1m[-1],           # Current price
                    volatility_1m,           # Volatility
                    ma_short / ma_long,      # MA ratio
                    np.mean(returns_1m[-5:]), # Recent returns
                    volumes_1m[-1] / np.mean(volumes_1m[-20:])  # Volume ratio
                ])
                
                preprocessing_results[symbol] = {
                    'data_points_1m': len(ohlcv_1m),
                    'data_points_5m': len(ohlcv_5m),
                    'feature_vector_size': len(feature_vector),
                    'volatility': float(volatility_1m),
                    'ma_ratio': float(ma_short / ma_long),
                    'data_quality': 'good' if len(ohlcv_1m) >= 150 and not np.isnan(feature_vector).any() else 'poor',
                    'success': bool(len(ohlcv_1m) >= 150 and not np.isnan(feature_vector).any())
                }
                
                logger.info(f"✅ {symbol}: {len(ohlcv_1m)} data points, volatility {volatility_1m:.4f}")
                
            except Exception as e:
                logger.error(f"❌ Data preprocessing failed for {symbol}: {e}")
                preprocessing_results[symbol] = {'error': str(e), 'success': False}
        
        successful_preprocessing = sum(1 for r in preprocessing_results.values() if r.get('success', False))
        
        result = {
            'test': 'Live Data Preprocessing for AI Models',
            'symbols_tested': len(self.test_symbols),
            'successful_preprocessing': successful_preprocessing,
            'preprocessing_results': preprocessing_results,
            'success': successful_preprocessing >= 1  # At least 1 symbol should work
        }
        
        self.results.append(result)
        return result['success']
    
    async def test_rl_agent_training_simulation(self):
        """Test RL agent training simulation with live data"""
        logger.info("🔧 Testing RL Agent Training Simulation")
        
        symbol = 'BTC/USDT:USDT'
        training_episodes = 10
        
        try:
            # Fetch live data for training
            ohlcv = self.exchange_engine.exchange.fetch_ohlcv(symbol, '1m', limit=500)
            prices = [candle[4] for candle in ohlcv]
            
            # Initialize training metrics
            episode_rewards = []
            episode_actions = []
            
            for episode in range(training_episodes):
                logger.info(f"Training episode {episode + 1}/{training_episodes}...")
                
                # Reset environment with live data
                self.trading_env.reset(prices=prices[episode*40:(episode+1)*40 + 10])
                
                episode_reward = 0
                episode_action_count = {'buy': 0, 'sell': 0, 'hold': 0}
                
                # Simulate trading episode
                for step in range(40):  # 40 steps per episode
                    # Get current state
                    state = self.trading_env.get_state()
                    
                    # Agent makes decision
                    action = self.rl_agent.get_action(state)
                    
                    # Execute action in environment
                    reward = self.trading_env.step(action)
                    
                    episode_reward += reward
                    episode_action_count[action] += 1
                
                episode_rewards.append(episode_reward)
                episode_actions.append(episode_action_count)
                
                logger.info(f"Episode {episode + 1}: Reward {episode_reward:.2f}")
            
            # Analyze training performance
            avg_reward = np.mean(episode_rewards)
            reward_trend = np.polyfit(range(len(episode_rewards)), episode_rewards, 1)[0]  # Linear trend
            
            # Check if agent is learning (improving over time)
            learning_detected = bool(reward_trend > 0 or avg_reward > 0)
            
            result = {
                'test': 'RL Agent Training Simulation',
                'training_episodes': training_episodes,
                'avg_episode_reward': float(avg_reward),
                'reward_trend': float(reward_trend),
                'final_episode_reward': float(episode_rewards[-1]),
                'learning_detected': learning_detected,
                'episode_rewards': [float(r) for r in episode_rewards],
                'success': learning_detected and len(episode_rewards) == training_episodes
            }
            
            self.results.append(result)
            logger.info(f"✅ RL training: {avg_reward:.2f} avg reward, trend: {reward_trend:.4f}")
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ RL agent training simulation failed: {e}")
            result = {
                'test': 'RL Agent Training Simulation',
                'error': str(e),
                'success': False
            }
            self.results.append(result)
            return False
    
    async def test_model_prediction_accuracy(self):
        """Test AI model prediction accuracy with live data"""
        logger.info("🔧 Testing Model Prediction Accuracy")
        
        symbol = 'BTC/USDT:USDT'
        prediction_tests = []
        
        try:
            # Fetch recent live data
            ohlcv = self.exchange_engine.exchange.fetch_ohlcv(symbol, '1m', limit=100)
            prices = [candle[4] for candle in ohlcv]
            
            # Test predictions for last 20 data points
            correct_predictions = 0
            total_predictions = 20
            
            for i in range(len(prices) - total_predictions, len(prices) - 1):
                current_price = prices[i]
                next_price = prices[i + 1]
                
                # Create state for prediction
                state = {
                    'price': current_price,
                    'recent_prices': prices[max(0, i-10):i],
                    'timestamp': time.time()
                }
                
                # Get model prediction
                predicted_action = self.rl_agent.get_action(state)
                
                # Determine actual market direction
                actual_direction = 'buy' if next_price > current_price else 'sell' if next_price < current_price else 'hold'
                
                # Check if prediction matches actual direction
                prediction_correct = (
                    (predicted_action == 'buy' and actual_direction == 'buy') or
                    (predicted_action == 'sell' and actual_direction == 'sell') or
                    (predicted_action == 'hold' and actual_direction == 'hold')
                )
                
                if prediction_correct:
                    correct_predictions += 1
                
                prediction_tests.append({
                    'predicted': predicted_action,
                    'actual': actual_direction,
                    'correct': prediction_correct,
                    'price_change': next_price - current_price
                })
            
            accuracy = correct_predictions / total_predictions
            
            result = {
                'test': 'Model Prediction Accuracy',
                'total_predictions': total_predictions,
                'correct_predictions': correct_predictions,
                'accuracy_percent': accuracy * 100,
                'prediction_details': prediction_tests,
                'success': accuracy >= 0.4  # 40% accuracy threshold (better than random for 3-class)
            }
            
            self.results.append(result)
            logger.info(f"✅ Prediction accuracy: {accuracy:.1%} ({correct_predictions}/{total_predictions})")
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Model prediction accuracy test failed: {e}")
            result = {
                'test': 'Model Prediction Accuracy',
                'error': str(e),
                'success': False
            }
            self.results.append(result)
            return False
    
    async def test_model_adaptation_to_market_conditions(self):
        """Test model adaptation to different market conditions"""
        logger.info("🔧 Testing Model Adaptation to Market Conditions")
        
        adaptation_tests = {}
        
        for symbol in self.test_symbols:
            try:
                logger.info(f"Testing market adaptation for {symbol}...")
                
                # Fetch data for different time periods
                recent_data = self.exchange_engine.exchange.fetch_ohlcv(symbol, '1m', limit=100)
                older_data = self.exchange_engine.exchange.fetch_ohlcv(symbol, '1h', limit=100)
                
                recent_prices = [candle[4] for candle in recent_data]
                older_prices = [candle[4] for candle in older_data]
                
                # Calculate market characteristics
                recent_volatility = np.std(np.diff(recent_prices) / recent_prices[:-1])
                older_volatility = np.std(np.diff(older_prices) / older_prices[:-1])
                
                recent_trend = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
                older_trend = (older_prices[-1] - older_prices[0]) / older_prices[0]
                
                # Test model behavior in different conditions
                volatile_state = {
                    'price': recent_prices[-1],
                    'volatility': recent_volatility,
                    'trend': recent_trend
                }
                
                stable_state = {
                    'price': recent_prices[-1],
                    'volatility': older_volatility,
                    'trend': 0.0  # Neutral trend
                }
                
                volatile_action = self.rl_agent.get_action(volatile_state)
                stable_action = self.rl_agent.get_action(stable_state)
                
                # Check if model adapts (different actions for different conditions)
                adaptation_detected = bool(volatile_action != stable_action or abs(recent_volatility - older_volatility) > 0.01)
                
                adaptation_tests[symbol] = {
                    'recent_volatility': float(recent_volatility),
                    'older_volatility': float(older_volatility),
                    'recent_trend': float(recent_trend),
                    'older_trend': float(older_trend),
                    'volatile_action': volatile_action,
                    'stable_action': stable_action,
                    'adaptation_detected': adaptation_detected,
                    'success': adaptation_detected
                }
                
                logger.info(f"✅ {symbol}: Adaptation {'detected' if adaptation_detected else 'not detected'}")
                
            except Exception as e:
                logger.error(f"❌ Market adaptation test failed for {symbol}: {e}")
                adaptation_tests[symbol] = {'error': str(e), 'success': False}
        
        successful_adaptations = sum(1 for test in adaptation_tests.values() if test.get('success', False))
        
        result = {
            'test': 'Model Adaptation to Market Conditions',
            'symbols_tested': len(self.test_symbols),
            'successful_adaptations': successful_adaptations,
            'adaptation_tests': adaptation_tests,
            'success': successful_adaptations >= 1  # At least 1 symbol should show adaptation
        }
        
        self.results.append(result)
        return result['success']
    
    def generate_ai_training_report(self):
        """Generate comprehensive AI model training report"""
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'total_tests': len(self.results),
            'passed_tests': sum(1 for r in self.results if r['success']),
            'failed_tests': sum(1 for r in self.results if not r['success']),
            'success_rate': sum(1 for r in self.results if r['success']) / len(self.results) * 100 if self.results else 0,
            'ai_training_tests': self.results
        }
        
        # Save report
        filename = f"ai_model_training_live_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        return report, filename

async def main():
    """Run all AI model training tests with live data"""
    print("🚀 Starting AI Model Training with Live Data Testing")
    print("=" * 60)
    
    tester = AIModelTrainingTester()
    
    # Setup AI training environment
    try:
        await tester.setup_ai_training_environment()
    except Exception as e:
        print(f"❌ Failed to setup AI training environment: {e}")
        return False
    
    # Run all AI training tests
    tests = [
        tester.test_live_data_preprocessing,
        tester.test_rl_agent_training_simulation,
        tester.test_model_prediction_accuracy,
        tester.test_model_adaptation_to_market_conditions,
    ]
    
    results = []
    for i, test in enumerate(tests, 1):
        print(f"\n🔧 Running AI training test {i}/{len(tests)}...")
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ AI training test {i} failed with exception: {e}")
            results.append(False)
    
    # Generate report
    report, filename = tester.generate_ai_training_report()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 AI MODEL TRAINING WITH LIVE DATA SUMMARY")
    print("=" * 60)
    
    for i, (test_result, success) in enumerate(zip(tester.results, results), 1):
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - Test {i}: {test_result['test']}")
    
    print(f"\nResults: {report['passed_tests']}/{report['total_tests']} tests passed")
    print(f"Success Rate: {report['success_rate']:.1f}%")
    print(f"📄 Detailed report saved to: {filename}")
    
    if report['success_rate'] >= 75:
        print("🎉 AI model training with live data testing PASSED!")
        return True
    else:
        print("⚠️ Some AI training tests failed. Review the detailed report.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
