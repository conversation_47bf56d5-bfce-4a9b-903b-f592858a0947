#!/usr/bin/env python3
"""
Epinnox Trading System - Production Credentials Manager
Secure credential management for live trading deployment
"""

import yaml
import os
from typing import Dict, Optional

class CredentialsManager:
    """Secure credentials manager for Epinnox trading system"""
    
    def __init__(self, credentials_file: str = "credentials.yaml"):
        self.credentials_file = credentials_file
        self.credentials = None
        self.load_credentials()
    
    def load_credentials(self):
        """Load credentials from YAML file"""
        try:
            with open(self.credentials_file, 'r') as f:
                self.credentials = yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Credentials file {self.credentials_file} not found")
        except Exception as e:
            raise Exception(f"Error loading credentials: {e}")
    
    def get_account_credentials(self, account_name: Optional[str] = None) -> Dict:
        """Get credentials for specified account or default account"""
        if not self.credentials:
            raise Exception("Credentials not loaded")
        
        # Use default account if none specified
        if not account_name:
            account_name = self.credentials.get('default_account', 'EPX')
        
        # Find account in accounts list
        accounts = self.credentials.get('accounts', [])
        for account in accounts:
            if account['name'].upper() == account_name.upper():
                return {
                    'name': account['name'],
                    'exchange': account['exchange'],
                    'api_key': account['api_key'],
                    'secret_key': account['secret_key'],
                    'description': account['description']
                }
        
        raise Exception(f"Account {account_name} not found in credentials")
    
    def get_htx_credentials(self) -> Dict:
        """Get HTX-specific credentials for backward compatibility"""
        if not self.credentials:
            raise Exception("Credentials not loaded")
        
        htx_creds = self.credentials.get('htx', {})
        return {
            'apiKey': htx_creds.get('apiKey'),
            'secret': htx_creds.get('secret'),
            'password': htx_creds.get('password', ''),
            'sandbox': htx_creds.get('sandbox', False)
        }
    
    def list_available_accounts(self) -> list:
        """List all available trading accounts"""
        if not self.credentials:
            return []
        
        accounts = self.credentials.get('accounts', [])
        return [
            {
                'name': acc['name'],
                'exchange': acc['exchange'],
                'description': acc['description']
            }
            for acc in accounts
            if acc['api_key'] != 'your_api_key_4'  # Exclude placeholder accounts
        ]

# Initialize global credentials manager
credentials_manager = CredentialsManager()

# Production Trading Account Configuration
# Using EPX account as default for conservative live trading
PRODUCTION_ACCOUNT = "EPX"  # Account with $10+ available

# Get production account credentials
try:
    production_creds = credentials_manager.get_account_credentials(PRODUCTION_ACCOUNT)
    
    # HTX API credentials for live trading
    HTX_API_KEY = production_creds['api_key']
    HTX_SECRET_KEY = production_creds['secret_key']
    HTX_PASSPHRASE = ""  # HTX doesn't require passphrase
    HTX_SANDBOX = False  # Live trading mode
    
    # Account information
    ACCOUNT_NAME = production_creds['name']
    ACCOUNT_DESCRIPTION = production_creds['description']
    EXCHANGE_NAME = production_creds['exchange']
    
    print(f"✅ Production credentials loaded for account: {ACCOUNT_NAME}")
    print(f"   Exchange: {EXCHANGE_NAME}")
    print(f"   Description: {ACCOUNT_DESCRIPTION}")
    print(f"   Mode: {'LIVE TRADING' if not HTX_SANDBOX else 'SANDBOX'}")
    
except Exception as e:
    print(f"❌ Error loading production credentials: {e}")
    print("⚠️  Please ensure credentials.yaml is properly configured")
    
    # Fallback credentials (will cause errors if used)
    HTX_API_KEY = "CREDENTIALS_NOT_LOADED"
    HTX_SECRET_KEY = "CREDENTIALS_NOT_LOADED"
    HTX_PASSPHRASE = ""
    HTX_SANDBOX = True
    ACCOUNT_NAME = "ERROR"
    ACCOUNT_DESCRIPTION = "Credentials not loaded"
    EXCHANGE_NAME = "none"

# Conservative Trading Configuration
CONSERVATIVE_SETTINGS = {
    "initial_balance": 10.0,  # Start with $10 minimum
    "max_portfolio_risk": 0.02,  # 2% maximum portfolio risk
    "max_position_size": 0.01,   # 1% maximum position size
    "max_leverage": 1.0,         # No leverage
    "max_daily_loss": 0.005,     # 0.5% daily loss limit
    "max_concurrent_positions": 1,  # Only 1 position at a time
    "minimum_confidence": 0.80,  # 80% minimum confidence
    "max_trades_per_day": 2,     # Maximum 2 trades per day
    "trading_symbol": "BTC/USDT:USDT",  # Only BTC initially
    "cooldown_minutes": 60       # 1 hour between trades
}

# Available accounts for switching (if needed)
AVAILABLE_ACCOUNTS = {
    "EPX": {
        "name": "EPX",
        "description": "Primary trading account",
        "recommended_balance": 10.0
    },
    "DM": {
        "name": "DM", 
        "description": "Secondary trading account",
        "recommended_balance": 10.0
    },
    "JEDI": {
        "name": "Jedi",
        "description": "Backup trading account (positions not working)",
        "recommended_balance": 10.0
    }
}

def switch_account(account_name: str) -> bool:
    """Switch to different trading account"""
    global HTX_API_KEY, HTX_SECRET_KEY, ACCOUNT_NAME, ACCOUNT_DESCRIPTION
    
    try:
        creds = credentials_manager.get_account_credentials(account_name)
        HTX_API_KEY = creds['api_key']
        HTX_SECRET_KEY = creds['secret_key']
        ACCOUNT_NAME = creds['name']
        ACCOUNT_DESCRIPTION = creds['description']
        
        print(f"✅ Switched to account: {ACCOUNT_NAME}")
        print(f"   Description: {ACCOUNT_DESCRIPTION}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to switch to account {account_name}: {e}")
        return False

def validate_credentials() -> bool:
    """Validate that credentials are properly loaded"""
    if HTX_API_KEY == "CREDENTIALS_NOT_LOADED":
        return False
    
    if not HTX_API_KEY or not HTX_SECRET_KEY:
        return False
    
    if len(HTX_API_KEY) < 10 or len(HTX_SECRET_KEY) < 10:
        return False
    
    return True

def get_account_info() -> Dict:
    """Get current account information"""
    return {
        "account_name": ACCOUNT_NAME,
        "account_description": ACCOUNT_DESCRIPTION,
        "exchange": EXCHANGE_NAME,
        "api_key_preview": HTX_API_KEY[:8] + "..." if HTX_API_KEY else "None",
        "sandbox_mode": HTX_SANDBOX,
        "credentials_valid": validate_credentials()
    }

# Security check on import
if __name__ == "__main__":
    print("🔐 Epinnox Credentials Manager")
    print("=" * 40)
    
    # Display account info
    info = get_account_info()
    for key, value in info.items():
        print(f"   {key}: {value}")
    
    # List available accounts
    print("\n📋 Available Accounts:")
    for account in credentials_manager.list_available_accounts():
        print(f"   • {account['name']}: {account['description']}")
    
    # Validation
    if validate_credentials():
        print("\n✅ Credentials validation: PASSED")
    else:
        print("\n❌ Credentials validation: FAILED")
