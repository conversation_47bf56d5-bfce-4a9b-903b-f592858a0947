#!/usr/bin/env python3
"""
Pre-Deployment Validation for Live Trading
Comprehensive validation before starting live autonomous trading
"""

import asyncio
import logging
import os
import sys
import yaml
import ccxt
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LiveTradingValidator:
    """
    Comprehensive validator for live trading deployment
    """
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = None
        self.exchange = None
        self.validation_results = {}
        
    async def run_validation(self):
        """Run comprehensive pre-deployment validation"""
        logger.info("🔍 Starting Live Trading Pre-Deployment Validation")
        logger.info("="*60)
        
        # Load configuration
        if not await self._load_configuration():
            return False
        
        # Validate API credentials
        if not await self._validate_api_credentials():
            return False
        
        # Validate account access
        if not await self._validate_account_access():
            return False
        
        # Validate trading permissions
        if not await self._validate_trading_permissions():
            return False
        
        # Validate account balance
        if not await self._validate_account_balance():
            return False
        
        # Validate risk parameters
        if not await self._validate_risk_parameters():
            return False
        
        # Validate safety systems
        if not await self._validate_safety_systems():
            return False
        
        # Validate system components
        if not await self._validate_system_components():
            return False
        
        # Generate final report
        return self._generate_final_report()
    
    async def _load_configuration(self):
        """Load and validate configuration file"""
        try:
            logger.info("📋 Loading configuration...")
            
            if not Path(self.config_path).exists():
                logger.error(f"❌ Configuration file not found: {self.config_path}")
                return False
            
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            
            # Validate required fields
            required_fields = [
                'trading_mode', 'symbols', 'max_daily_loss', 
                'max_position_size', 'max_leverage', 'exchange'
            ]
            
            missing_fields = [field for field in required_fields if field not in self.config]
            if missing_fields:
                logger.error(f"❌ Missing required configuration fields: {missing_fields}")
                return False
            
            # Validate trading mode
            if self.config['trading_mode'] != 'live':
                logger.error(f"❌ Trading mode must be 'live', got: {self.config['trading_mode']}")
                return False
            
            logger.info("✅ Configuration loaded and validated")
            return True
            
        except Exception as e:
            logger.error(f"❌ Configuration loading failed: {e}")
            return False
    
    async def _validate_api_credentials(self):
        """Validate exchange API credentials"""
        try:
            logger.info("🔑 Validating API credentials...")
            
            # Check environment variables
            required_env_vars = ['HTX_API_KEY', 'HTX_SECRET_KEY']
            missing_vars = [var for var in required_env_vars if not os.getenv(var)]
            
            if missing_vars:
                logger.error(f"❌ Missing environment variables: {missing_vars}")
                logger.error("Please set HTX_API_KEY and HTX_SECRET_KEY environment variables")
                return False
            
            # Initialize exchange
            exchange_config = {
                'apiKey': os.getenv('HTX_API_KEY'),
                'secret': os.getenv('HTX_SECRET_KEY'),
                'sandbox': False,  # Live trading
                'enableRateLimit': True,
            }
            
            # Add passphrase if available
            if os.getenv('HTX_PASSPHRASE'):
                exchange_config['password'] = os.getenv('HTX_PASSPHRASE')
            
            self.exchange = ccxt.htx(exchange_config)
            
            logger.info("✅ API credentials configured")
            return True
            
        except Exception as e:
            logger.error(f"❌ API credential validation failed: {e}")
            return False
    
    async def _validate_account_access(self):
        """Validate exchange account access"""
        try:
            logger.info("🏦 Validating account access...")
            
            # Test API connection
            await asyncio.sleep(0.1)  # Rate limiting
            balance = self.exchange.fetch_balance()
            
            if not balance:
                logger.error("❌ Unable to fetch account balance")
                return False
            
            logger.info("✅ Account access validated")
            logger.info(f"📊 Account currencies available: {list(balance.keys())}")
            return True
            
        except ccxt.AuthenticationError as e:
            logger.error(f"❌ Authentication failed: {e}")
            logger.error("Please check your API credentials")
            return False
        except ccxt.PermissionDenied as e:
            logger.error(f"❌ Permission denied: {e}")
            logger.error("Please check API key permissions")
            return False
        except Exception as e:
            logger.error(f"❌ Account access validation failed: {e}")
            return False
    
    async def _validate_trading_permissions(self):
        """Validate trading permissions"""
        try:
            logger.info("🔐 Validating trading permissions...")
            
            # Check if futures trading is available
            if hasattr(self.exchange, 'has') and self.exchange.has.get('future'):
                logger.info("✅ Futures trading supported")
            else:
                logger.warning("⚠️  Futures trading support unclear")
            
            # Test market data access for configured symbols
            for symbol in self.config['symbols']:
                try:
                    ticker = self.exchange.fetch_ticker(symbol)
                    logger.info(f"✅ Market data access for {symbol}: ${ticker['last']:.2f}")
                except Exception as e:
                    logger.error(f"❌ Cannot access market data for {symbol}: {e}")
                    return False
            
            logger.info("✅ Trading permissions validated")
            return True
            
        except Exception as e:
            logger.error(f"❌ Trading permissions validation failed: {e}")
            return False
    
    async def _validate_account_balance(self):
        """Validate account balance and margin"""
        try:
            logger.info("💰 Validating account balance...")
            
            balance = self.exchange.fetch_balance()
            
            # Check USDT balance (main trading currency)
            usdt_balance = balance.get('USDT', {})
            free_balance = usdt_balance.get('free', 0)
            total_balance = usdt_balance.get('total', 0)
            
            logger.info(f"💵 USDT Balance - Free: ${free_balance:.2f}, Total: ${total_balance:.2f}")
            
            # Validate minimum balance
            min_required = 50.0  # Expected account balance
            if free_balance < min_required:
                logger.error(f"❌ Insufficient balance: ${free_balance:.2f} < ${min_required:.2f}")
                return False
            
            # Check if balance matches configuration
            expected_balance = self.config.get('initial_balance', 50.0)
            if abs(free_balance - expected_balance) > 5.0:  # Allow 5% variance
                logger.warning(f"⚠️  Balance variance: Expected ${expected_balance:.2f}, Got ${free_balance:.2f}")
            
            # Validate risk parameters against actual balance
            max_daily_loss = abs(self.config['max_daily_loss'])
            max_position_size = self.config['max_position_size']
            
            if max_daily_loss > free_balance * 0.5:
                logger.error(f"❌ Daily loss limit too high: ${max_daily_loss:.2f} > 50% of balance")
                return False
            
            if max_position_size > free_balance * 0.5:
                logger.error(f"❌ Position size limit too high: ${max_position_size:.2f} > 50% of balance")
                return False
            
            logger.info("✅ Account balance validated")
            return True
            
        except Exception as e:
            logger.error(f"❌ Account balance validation failed: {e}")
            return False
    
    async def _validate_risk_parameters(self):
        """Validate risk management parameters"""
        try:
            logger.info("⚖️  Validating risk parameters...")
            
            # Extract risk parameters
            max_daily_loss = abs(self.config['max_daily_loss'])
            max_position_size = self.config['max_position_size']
            max_leverage = self.config['max_leverage']
            max_positions = self.config['max_open_positions']
            min_balance = self.config['min_account_balance']
            
            # Validate parameter ranges
            if max_daily_loss > 25.0:  # More than $25 loss
                logger.error(f"❌ Daily loss limit too high for small account: ${max_daily_loss:.2f}")
                return False
            
            if max_position_size > 20.0:  # More than $20 per position
                logger.error(f"❌ Position size too large for small account: ${max_position_size:.2f}")
                return False
            
            if max_leverage > 5.0:  # More than 5x leverage
                logger.error(f"❌ Leverage too high for conservative trading: {max_leverage}x")
                return False
            
            if max_positions > 3:  # More than 3 positions
                logger.error(f"❌ Too many concurrent positions for small account: {max_positions}")
                return False
            
            # Log risk parameters
            logger.info(f"📊 Risk Parameters:")
            logger.info(f"   Max Daily Loss: ${max_daily_loss:.2f}")
            logger.info(f"   Max Position Size: ${max_position_size:.2f}")
            logger.info(f"   Max Leverage: {max_leverage}x")
            logger.info(f"   Max Positions: {max_positions}")
            logger.info(f"   Min Balance: ${min_balance:.2f}")
            
            logger.info("✅ Risk parameters validated")
            return True
            
        except Exception as e:
            logger.error(f"❌ Risk parameter validation failed: {e}")
            return False
    
    async def _validate_safety_systems(self):
        """Validate safety and monitoring systems"""
        try:
            logger.info("🛡️ Validating safety systems...")
            
            # Test safety monitor initialization
            from core.safety_monitor import SafetyMonitor
            safety_monitor = SafetyMonitor(self.config)
            
            # Test emergency procedures configuration
            emergency_config = self.config.get('emergency', {})
            required_emergency_features = [
                'auto_stop_on_daily_loss',
                'auto_stop_on_low_balance', 
                'emergency_close_all_positions'
            ]
            
            for feature in required_emergency_features:
                if not emergency_config.get(feature, False):
                    logger.error(f"❌ Emergency feature not enabled: {feature}")
                    return False
            
            logger.info("✅ Safety systems validated")
            return True
            
        except Exception as e:
            logger.error(f"❌ Safety system validation failed: {e}")
            return False
    
    async def _validate_system_components(self):
        """Validate system components"""
        try:
            logger.info("🔧 Validating system components...")
            
            # Test orchestrator initialization
            from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator
            orchestrator = AutonomousTradingOrchestrator(self.config)
            
            # Test LLM analyzer
            from core.enhanced_llm_analyzer import EnhancedLLMAnalyzer
            llm_analyzer = EnhancedLLMAnalyzer(self.config)
            
            # Test simulation executor (for testing)
            from execution.simulation_executor import SimulationExecutor
            sim_executor = SimulationExecutor()
            
            logger.info("✅ System components validated")
            return True
            
        except Exception as e:
            logger.error(f"❌ System component validation failed: {e}")
            return False
    
    def _generate_final_report(self):
        """Generate final validation report"""
        logger.info("\n" + "="*60)
        logger.info("📊 LIVE TRADING PRE-DEPLOYMENT VALIDATION REPORT")
        logger.info("="*60)
        
        logger.info("✅ Configuration: VALIDATED")
        logger.info("✅ API Credentials: VALIDATED") 
        logger.info("✅ Account Access: VALIDATED")
        logger.info("✅ Trading Permissions: VALIDATED")
        logger.info("✅ Account Balance: VALIDATED")
        logger.info("✅ Risk Parameters: VALIDATED")
        logger.info("✅ Safety Systems: VALIDATED")
        logger.info("✅ System Components: VALIDATED")
        
        logger.info("\n🎯 DEPLOYMENT STATUS: READY FOR LIVE TRADING")
        logger.info("="*60)
        
        # Final safety reminder
        logger.warning("\n🚨 LIVE TRADING SAFETY REMINDER:")
        logger.warning("• This will trade with REAL MONEY")
        logger.warning("• Monitor the system continuously")
        logger.warning("• Emergency stop procedures are active")
        logger.warning("• Risk limits are enforced")
        logger.warning("• Start with close monitoring")
        
        return True

async def main():
    """Main validation function"""
    config_path = "config/live_production.yaml"
    
    if not Path(config_path).exists():
        logger.error(f"❌ Configuration file not found: {config_path}")
        sys.exit(1)
    
    validator = LiveTradingValidator(config_path)
    success = await validator.run_validation()
    
    if success:
        logger.info("\n✅ PRE-DEPLOYMENT VALIDATION PASSED")
        logger.info("System is ready for live trading deployment")
    else:
        logger.error("\n❌ PRE-DEPLOYMENT VALIDATION FAILED")
        logger.error("Please fix the issues before deploying")
        sys.exit(1)

if __name__ == '__main__':
    asyncio.run(main())
