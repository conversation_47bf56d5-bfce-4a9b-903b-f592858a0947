#!/usr/bin/env python3
"""
Epinnox Live Trading Monitor Dashboard
Real-time monitoring of live trading performance
"""

import json
import time
from datetime import datetime

def monitor_live_trading():
    """Monitor live trading session"""
    print("📊 EPINNOX LIVE TRADING MONITOR")
    print("=" * 50)
    
    while True:
        try:
            # Read latest log file
            import glob
            log_files = glob.glob("epinnox_live_*.log")
            if log_files:
                latest_log = max(log_files, key=lambda x: os.path.getctime(x))
                
                # Display last few lines
                with open(latest_log, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"\n📈 Latest Activity ({datetime.now().strftime('%H:%M:%S')}):")
                        for line in lines[-5:]:
                            print(f"   {line.strip()}")
            
            # Check for reports
            report_files = glob.glob("live_trading_report_*.json")
            if report_files:
                latest_report = max(report_files, key=lambda x: os.path.getctime(x))
                
                with open(latest_report, 'r') as f:
                    report = json.load(f)
                    
                metrics = report.get('performance_metrics', {})
                print(f"\n💰 Performance Summary:")
                print(f"   • Total Trades: {metrics.get('total_trades', 0)}")
                print(f"   • Total PnL: ${metrics.get('total_pnl', 0):.2f}")
                print(f"   • Max Drawdown: ${metrics.get('max_drawdown', 0):.2f}")
            
            time.sleep(30)  # Update every 30 seconds
            
        except KeyboardInterrupt:
            print("\n👋 Monitoring stopped")
            break
        except Exception as e:
            print(f"❌ Monitor error: {e}")
            time.sleep(10)

if __name__ == "__main__":
    monitor_live_trading()
