{"timestamp": "2025-06-27T03:48:56.837984", "duration": 18.865387678146362, "results": {"import_tests": {"status": "PASSED", "failed_imports": [], "total_modules": 7, "failed_count": 0}, "mock_exchange": {"status": "PASSED", "tests_run": ["balance", "order_creation", "ticker"]}, "autonomous_system": {"status": "PASSED", "components_tested": ["executor", "portfolio", "tracker"]}, "unit_tests": {"status": "FAILED", "output": "============================= test session starts =============================\nplatform win32 -- Python 3.13.2, pytest-8.3.5, pluggy-1.5.0 -- C:\\Users\\<USER>\\miniconda3\\python.exe\ncachedir: .pytest_cache\nrootdir: C:\\Users\\<USER>\\Documents\\dev\\Epinnox_v6\nconfigfile: pytest.ini\nplugins: anyio-4.9.0, asyncio-0.26.0, cov-6.2.1\nasyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... collected 126 items / 8 errors\n\n=================================== ERRORS ====================================\n________________ ERROR collecting tests/gpu/test_gpu_utils.py _________________\nImportError while importing test module 'C:\\Users\\<USER>\\Documents\\dev\\Epinnox_v6\\tests\\gpu\\test_gpu_utils.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\n..\\..\\..\\miniconda3\\Lib\\importlib\\__init__.py:88: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\ntests\\gpu\\test_gpu_utils.py:18: in <module>\n    from gpu_utils import check_gpu_availability, get_device, get_optimal_dtype\nE   ModuleNotFoundError: No module named 'gpu_utils'\n_____________ ERROR collecting tests/models/test_model_loader.py ______________\nImportError while importing test module 'C:\\Users\\<USER>\\Documents\\dev\\Epinnox_v6\\tests\\models\\test_model_loader.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\n..\\..\\..\\miniconda3\\Lib\\importlib\\__init__.py:88: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\ntests\\models\\test_model_loader.py:18: in <module>\n    from models.model_loader import ModelLoader\nmodels\\model_loader.py:10: in <module>\n    from gpu_utils import check_gpu_availability, get_device, get_optimal_dtype, optimize_for_gpu\nE   ModuleNotFoundError: No module named 'gpu_utils'\n__________________ ERROR collecting tests/test_dashboard.py ___________________\nImportError while importing test module 'C:\\Users\\<USER>\\Documents\\dev\\Epinnox_v6\\tests\\test_dashboard.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\n..\\..\\..\\miniconda3\\Lib\\importlib\\__init__.py:88: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\ntests\\test_dashboard.py:3: in <module>\n    from gui.panels.dashboard_panel import DashboardPanel\nE   ModuleNotFoundError: No module named 'gui.panels'\n_____________ ERROR collecting tests/test_dashboard_interface.py ______________\nImportError while importing test module 'C:\\Users\\<USER>\\Documents\\dev\\Epinnox_v6\\tests\\test_dashboard_interface.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\n..\\..\\..\\miniconda3\\Lib\\importlib\\__init__.py:88: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\ntests\\test_dashboard_interface.py:12: in <module>\n    from gui.panels.dashboard_panel import DashboardPanel\nE   ModuleNotFoundError: No module named 'gui.panels'\n_________________ ERROR collecting tests/test_integration.py __________________\nImportError while importing test module 'C:\\Users\\<USER>\\Documents\\dev\\Epinnox_v6\\tests\\test_integration.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\n..\\..\\..\\miniconda3\\Lib\\importlib\\__init__.py:88: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\ntests\\test_integration.py:12: in <module>\n    from core.main import process_market_data\nE   ModuleNotFoundError: No module named 'core.main'\n__________________ ERROR collecting tests/test_live_data.py ___________________\nImportError while importing test module 'C:\\Users\\<USER>\\Documents\\dev\\Epinnox_v6\\tests\\test_live_data.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\n..\\..\\..\\miniconda3\\Lib\\importlib\\__init__.py:88: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\ntests\\test_live_data.py:8: in <module>\n    from gui_integration import TradingSystemGUIIntegration\nE   ModuleNotFoundError: No module named 'gui_integration'\n________________ ERROR collecting tests/test_reorganized_ui.py ________________\nImportError while importing test module 'C:\\Users\\<USER>\\Documents\\dev\\Epinnox_v6\\tests\\test_reorganized_ui.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\n..\\..\\..\\miniconda3\\Lib\\importlib\\__init__.py:88: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\ntests\\test_reorganized_ui.py:13: in <module>\n    from gui.main_window import TradingSystemGUI\nE   ModuleNotFoundError: No module named 'gui.main_window'\n_________________ ERROR collecting tests/ui/test_dashboard.py _________________\nImportError while importing test module 'C:\\Users\\<USER>\\Documents\\dev\\Epinnox_v6\\tests\\ui\\test_dashboard.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\n..\\..\\..\\miniconda3\\Lib\\importlib\\__init__.py:88: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\ntests\\ui\\test_dashboard.py:3: in <module>\n    from gui.panels.dashboard_panel import DashboardPanel\nE   ModuleNotFoundError: No module named 'gui.panels'\n=========================== short test summary info ===========================\nERROR tests/gpu/test_gpu_utils.py\nERROR tests/models/test_model_loader.py\nERROR tests/test_dashboard.py\nERROR tests/test_dashboard_interface.py\nERROR tests/test_integration.py\nERROR tests/test_live_data.py\nERROR tests/test_reorganized_ui.py\nERROR tests/ui/test_dashboard.py\n!!!!!!!!!!!!!!!!!!! Interrupted: 8 errors during collection !!!!!!!!!!!!!!!!!!!\n============================== 8 errors in 9.70s ==============================\n", "errors": "C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pytest_asyncio\\plugin.py:217: PytestDeprecationWarning: The configuration option \"asyncio_default_fixture_loop_scope\" is unset.\nThe event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: \"function\", \"class\", \"module\", \"package\", \"session\"\n\n  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))\n", "return_code": 2}, "integration_tests": {"status": "FAILED", "output": "============================= test session starts =============================\nplatform win32 -- Python 3.13.2, pytest-8.3.5, pluggy-1.5.0 -- C:\\Users\\<USER>\\miniconda3\\python.exe\ncachedir: .pytest_cache\nrootdir: C:\\Users\\<USER>\\Documents\\dev\\Epinnox_v6\nconfigfile: pytest.ini\nplugins: anyio-4.9.0, asyncio-0.26.0, cov-6.2.1\nasyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... collected 8 items\n\ntests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_full_trading_cycle FAILED [ 12%]\ntests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_risk_management_integration FAILED [ 25%]\ntests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_portfolio_position_updates FAILED [ 37%]\ntests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_stop_loss_integration FAILED [ 50%]\ntests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_performance_tracking_integration PASSED [ 62%]\ntests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_autonomous_controller_initialization PASSED [ 75%]\ntests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_decision_to_execution_flow FAILED [ 87%]\ntests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_error_handling_integration PASSED [100%]\n\n================================== FAILURES ===================================\n___________ TestAutonomousSystemIntegration.test_full_trading_cycle ___________\n\nself = <tests.test_autonomous_integration.TestAutonomousSystemIntegration object at 0x000002228F751090>\nautonomous_executor = <execution.autonomous_executor.AutonomousTradeExecutor object at 0x000002228F7E9400>\nportfolio_manager = <portfolio.portfolio_manager.PortfolioManager object at 0x000002228F7E9550>\nperformance_tracker = <monitoring.performance_tracker.PerformanceTracker object at 0x000002228F7E96A0>\n\n    @pytest.mark.asyncio\n    async def test_full_trading_cycle(self, autonomous_executor, portfolio_manager, performance_tracker):\n        \"\"\"Test complete trading cycle: decision -> execution -> portfolio -> tracking\"\"\"\n    \n        # 1. Create trading decision\n        decision_data = {\n            'decision': 'LONG',\n            'confidence': 75,\n            'selected_symbol': 'BTC/USDT',\n            'leverage_position_sizing': {\n                'position_units': 0.1,\n                'position_usd': 5000.0,\n                'effective_leverage': 2.0,\n                'stop_loss_price': 49000.0,\n                'take_profit_price': 52000.0\n            }\n        }\n    \n        # 2. Execute trade\n        execution_result = await autonomous_executor.execute_trading_decision(decision_data)\n        assert execution_result['status'] == 'FILLED'\n    \n        # 3. Update portfolio\n>       await portfolio_manager.open_position(\n            symbol=execution_result['symbol'],\n            side=execution_result['side'],\n            size=execution_result['amount'],\n            entry_price=execution_result['price'],\n            leverage=decision_data['leverage_position_sizing']['effective_leverage']\n        )\n\ntests\\test_autonomous_integration.py:67: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <portfolio.portfolio_manager.PortfolioManager object at 0x000002228F7E9550>\nsymbol = 'BTC/USDT', side = 'buy', size = 0.1, entry_price = 50238.64001044348\nleverage = 2.0, stop_loss = None, take_profit = None\n\n    async def open_position(self, symbol: str, side: str, size: float, entry_price: float,\n                          leverage: float = 1.0, stop_loss: float = None, take_profit: float = None):\n        \"\"\"Open a new position\"\"\"\n        position_size_usd = size * entry_price\n    \n        # Check if position can be opened\n        check_result = await self.can_open_position(symbol, position_size_usd, leverage)\n        if not check_result['allowed']:\n>           raise Exception(f\"Cannot open position: {check_result['reason']}\")\nE           Exception: Cannot open position: Position size 50.2% exceeds limit 10.0%\n\nportfolio\\portfolio_manager.py:116: Exception\n______ TestAutonomousSystemIntegration.test_risk_management_integration _______\n\nself = <tests.test_autonomous_integration.TestAutonomousSystemIntegration object at 0x000002228F751590>\nautonomous_executor = <execution.autonomous_executor.AutonomousTradeExecutor object at 0x000002228F7520D0>\nportfolio_manager = <portfolio.portfolio_manager.PortfolioManager object at 0x000002228F752490>\n\n    @pytest.mark.asyncio\n    async def test_risk_management_integration(self, autonomous_executor, portfolio_manager):\n        \"\"\"Test risk management across executor and portfolio manager\"\"\"\n    \n        # Fill portfolio to near capacity\n        for i in range(4):\n            await portfolio_manager.open_position(\n                symbol=f'TEST{i}/USDT',\n                side='long',\n                size=0.1,\n                entry_price=100.0,\n                leverage=1.0\n            )\n    \n        # Try to execute trade that would exceed position limits\n        decision_data = {\n            'decision': 'LONG',\n            'confidence': 75,\n            'selected_symbol': 'BTC/USDT',\n            'leverage_position_sizing': {\n                'position_units': 0.1,\n                'position_usd': 2000.0,  # Large position\n                'effective_leverage': 5.0  # High leverage\n            }\n        }\n    \n        # Should be blocked by risk management\n        result = await autonomous_executor.execute_trading_decision(decision_data)\n    \n        # Verify risk management worked\n>       assert result['status'] in ['RISK_REJECTED', 'SKIPPED']\nE       AssertionError: assert 'FILLED' in ['RISK_REJECTED', 'SKIPPED']\n\ntests\\test_autonomous_integration.py:128: AssertionError\n_______ TestAutonomousSystemIntegration.test_portfolio_position_updates _______\n\nself = <tests.test_autonomous_integration.TestAutonomousSystemIntegration object at 0x000002228F5F3360>\nportfolio_manager = <portfolio.portfolio_manager.PortfolioManager object at 0x000002228F753390>\nmock_exchange = <tests.mocks.mock_exchange.MockExchange object at 0x000002228F7534D0>\n\n    @pytest.mark.asyncio\n    async def test_portfolio_position_updates(self, portfolio_manager, mock_exchange):\n        \"\"\"Test portfolio position updates with market data\"\"\"\n    \n        # Open positions\n>       await portfolio_manager.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0)\n\ntests\\test_autonomous_integration.py:135: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <portfolio.portfolio_manager.PortfolioManager object at 0x000002228F753390>\nsymbol = 'BTC/USDT', side = 'long', size = 0.1, entry_price = 50000.0\nleverage = 2.0, stop_loss = None, take_profit = None\n\n    async def open_position(self, symbol: str, side: str, size: float, entry_price: float,\n                          leverage: float = 1.0, stop_loss: float = None, take_profit: float = None):\n        \"\"\"Open a new position\"\"\"\n        position_size_usd = size * entry_price\n    \n        # Check if position can be opened\n        check_result = await self.can_open_position(symbol, position_size_usd, leverage)\n        if not check_result['allowed']:\n>           raise Exception(f\"Cannot open position: {check_result['reason']}\")\nE           Exception: Cannot open position: Position size 50.0% exceeds limit 10.0%\n\nportfolio\\portfolio_manager.py:116: Exception\n_________ TestAutonomousSystemIntegration.test_stop_loss_integration __________\n\nself = <tests.test_autonomous_integration.TestAutonomousSystemIntegration object at 0x000002228F5F2FD0>\nportfolio_manager = <portfolio.portfolio_manager.PortfolioManager object at 0x000002228F974640>\nmock_exchange = <tests.mocks.mock_exchange.MockExchange object at 0x000002228F974770>\n\n    @pytest.mark.asyncio\n    async def test_stop_loss_integration(self, portfolio_manager, mock_exchange):\n        \"\"\"Test stop loss triggering across portfolio and exchange\"\"\"\n    \n        # Open position with stop loss\n>       await portfolio_manager.open_position(\n            'BTC/USDT', 'long', 0.1, 50000.0, 2.0,\n            stop_loss=49000.0\n        )\n\ntests\\test_autonomous_integration.py:162: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <portfolio.portfolio_manager.PortfolioManager object at 0x000002228F974640>\nsymbol = 'BTC/USDT', side = 'long', size = 0.1, entry_price = 50000.0\nleverage = 2.0, stop_loss = 49000.0, take_profit = None\n\n    async def open_position(self, symbol: str, side: str, size: float, entry_price: float,\n                          leverage: float = 1.0, stop_loss: float = None, take_profit: float = None):\n        \"\"\"Open a new position\"\"\"\n        position_size_usd = size * entry_price\n    \n        # Check if position can be opened\n        check_result = await self.can_open_position(symbol, position_size_usd, leverage)\n        if not check_result['allowed']:\n>           raise Exception(f\"Cannot open position: {check_result['reason']}\")\nE           Exception: Cannot open position: Position size 50.0% exceeds limit 10.0%\n\nportfolio\\portfolio_manager.py:116: Exception\n_______ TestAutonomousSystemIntegration.test_decision_to_execution_flow _______\n\nself = <tests.test_autonomous_integration.TestAutonomousSystemIntegration object at 0x000002228F769AE0>\nautonomous_executor = <execution.autonomous_executor.AutonomousTradeExecutor object at 0x000002228F9755B0>\nmock_exchange = <tests.mocks.mock_exchange.MockExchange object at 0x000002228F787410>\n\n    @pytest.mark.asyncio\n    async def test_decision_to_execution_flow(self, autonomous_executor, mock_exchange):\n        \"\"\"Test the flow from trading decision to actual execution\"\"\"\n    \n        # Test different decision types\n        decisions = [\n            {\n                'decision': 'LONG',\n                'confidence': 75,\n                'selected_symbol': 'BTC/USDT',\n                'leverage_position_sizing': {\n                    'position_units': 0.1,\n                    'position_usd': 5000.0,\n                    'effective_leverage': 2.0\n                }\n            },\n            {\n                'decision': 'WAIT',\n                'confidence': 45,\n                'selected_symbol': 'ETH/USDT'\n            },\n            {\n                'decision': 'SHORT',\n                'confidence': 80,\n                'selected_symbol': 'ADA/USDT',\n                'leverage_position_sizing': {\n                    'position_units': 100.0,\n                    'position_usd': 50.0,\n                    'effective_leverage': 1.5\n                }\n            }\n        ]\n    \n        results = []\n        for decision in decisions:\n            result = await autonomous_executor.execute_trading_decision(decision)\n            results.append(result)\n    \n        # Verify results\n        assert results[0]['status'] == 'FILLED'  # LONG with high confidence\n>       assert results[1]['status'] == 'WAIT'    # WAIT decision\nE       AssertionError: assert 'SKIPPED' == 'WAIT'\nE         \nE         - WAIT\nE         + SKIPPED\n\ntests\\test_autonomous_integration.py:274: AssertionError\n============================== warnings summary ===============================\ntests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_performance_tracking_integration\ntests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_performance_tracking_integration\ntests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_performance_tracking_integration\n  C:\\Users\\<USER>\\Documents\\dev\\Epinnox_v6\\monitoring\\performance_tracker.py:81: DeprecationWarning: The default datetime adapter is deprecated as of Python 3.12; see the sqlite3 documentation for suggested replacement recipes\n    cursor.execute('''\n\ntests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_performance_tracking_integration\n  C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pandas\\io\\sql.py:2674: DeprecationWarning: The default date adapter is deprecated as of Python 3.12; see the sqlite3 documentation for suggested replacement recipes\n    cur.execute(sql, *args)\n\ntests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_performance_tracking_integration\n  C:\\Users\\<USER>\\Documents\\dev\\Epinnox_v6\\monitoring\\performance_tracker.py:167: DeprecationWarning: The default date adapter is deprecated as of Python 3.12; see the sqlite3 documentation for suggested replacement recipes\n    cursor.execute('''\n\n-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html\n=========================== short test summary info ===========================\nFAILED tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_full_trading_cycle\nFAILED tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_risk_management_integration\nFAILED tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_portfolio_position_updates\nFAILED tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_stop_loss_integration\nFAILED tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_decision_to_execution_flow\n=================== 5 failed, 3 passed, 5 warnings in 2.44s ===================\n", "errors": "C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pytest_asyncio\\plugin.py:217: PytestDeprecationWarning: The configuration option \"asyncio_default_fixture_loop_scope\" is unset.\nThe event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: \"function\", \"class\", \"module\", \"package\", \"session\"\n\n  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))\n", "return_code": 1}}}