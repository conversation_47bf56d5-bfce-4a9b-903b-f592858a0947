"""
Portfolio Manager for Autonomous Trading
Manages multiple positions, risk, and portfolio-level decisions
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime
import asyncio
import logging

logger = logging.getLogger(__name__)

@dataclass
class Position:
    symbol: str
    side: str  # 'long' or 'short'
    size: float
    entry_price: float
    current_price: float
    leverage: float
    unrealized_pnl: float
    realized_pnl: float
    timestamp: datetime
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None

class PortfolioManager:
    """
    Advanced portfolio manager for autonomous trading
    """
    
    def __init__(self, initial_balance: float = 1000.0, max_positions: int = 5):
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.max_positions = max_positions
        self.positions: Dict[str, Position] = {}
        self.trade_history = []
        
        # Risk management settings
        self.max_portfolio_risk = 0.20  # 20% max portfolio risk
        self.max_position_size = 0.30   # 30% max per position (increased for crypto trading)
        self.max_leverage = 10.0
        self.max_daily_loss = 0.05      # 5% max daily loss
        
        # Performance tracking
        self.daily_pnl = 0.0
        self.peak_balance = initial_balance
        self.max_drawdown = 0.0
        
        logger.info(f"Portfolio manager initialized with ${initial_balance:.2f} balance")
    
    async def can_open_position(self, symbol: str, position_size_usd: float, leverage: float) -> Dict:
        """Check if a new position can be opened"""
        
        # Check 1: Maximum number of positions
        if len(self.positions) >= self.max_positions and symbol not in self.positions:
            return {
                'allowed': False,
                'reason': f'Maximum positions ({self.max_positions}) reached'
            }
        
        # Check 2: Position size limits
        position_risk = position_size_usd / self.current_balance
        if position_risk > self.max_position_size:
            return {
                'allowed': False,
                'reason': f'Position size {position_risk:.1%} exceeds limit {self.max_position_size:.1%}'
            }
        
        # Check 3: Leverage limits
        if leverage > self.max_leverage:
            return {
                'allowed': False,
                'reason': f'Leverage {leverage:.1f}x exceeds limit {self.max_leverage:.1f}x'
            }
        
        # Check 4: Daily loss limits
        if self.daily_pnl < -self.current_balance * self.max_daily_loss:
            return {
                'allowed': False,
                'reason': f'Daily loss limit reached: ${self.daily_pnl:.2f}'
            }
        
        # Check 5: Portfolio risk
        current_risk = self.calculate_total_portfolio_risk()
        new_position_risk = position_risk * leverage
        total_risk = current_risk + new_position_risk
        if total_risk > self.max_portfolio_risk:
            return {
                'allowed': False,
                'reason': f'Portfolio risk {total_risk:.1%} would exceed limit {self.max_portfolio_risk:.1%}'
            }
        
        return {'allowed': True, 'reason': 'All checks passed'}
    
    def calculate_total_portfolio_risk(self) -> float:
        """Calculate total portfolio risk exposure as a decimal (0.0 to 1.0)"""
        total_risk = 0.0

        for position in self.positions.values():
            position_value = position.size * position.current_price
            # Calculate risk exposure as a percentage in decimal form (0.0 to 1.0)
            risk_exposure = (position_value * position.leverage) / self.current_balance / 100.0
            total_risk += risk_exposure

        return total_risk
    
    async def open_position(self, symbol: str, side: str, size: float, entry_price: float, 
                          leverage: float = 1.0, stop_loss: float = None, take_profit: float = None):
        """Open a new position"""
        position_size_usd = size * entry_price
        
        # Check if position can be opened
        check_result = await self.can_open_position(symbol, position_size_usd, leverage)
        if not check_result['allowed']:
            raise Exception(f"Cannot open position: {check_result['reason']}")
        
        # Close existing position if switching sides
        if symbol in self.positions:
            existing_side = self.positions[symbol].side
            if existing_side != side:
                await self.close_position(symbol, entry_price)
        
        # Create new position
        position = Position(
            symbol=symbol,
            side=side,
            size=size,
            entry_price=entry_price,
            current_price=entry_price,
            leverage=leverage,
            unrealized_pnl=0.0,
            realized_pnl=0.0,
            timestamp=datetime.now(),
            stop_loss=stop_loss,
            take_profit=take_profit
        )
        
        self.positions[symbol] = position
        
        logger.info(f"[PORTFOLIO] Opened {side.upper()} position: {size:.2f} {symbol} @ ${entry_price:.4f} (leverage: {leverage:.1f}x)")
        
        return position
    
    async def close_position(self, symbol: str, exit_price: float):
        """Close an existing position"""
        if symbol not in self.positions:
            raise Exception(f"No position found for {symbol}")
        
        position = self.positions[symbol]
        
        # Calculate PnL
        if position.side == 'long':
            pnl_per_unit = exit_price - position.entry_price
        else:  # short
            pnl_per_unit = position.entry_price - exit_price
        
        total_pnl = pnl_per_unit * position.size * position.leverage
        pnl_percentage = (pnl_per_unit / position.entry_price) * position.leverage * 100
        
        # Update balance
        self.current_balance += total_pnl
        self.daily_pnl += total_pnl
        
        # Update performance metrics
        if self.current_balance > self.peak_balance:
            self.peak_balance = self.current_balance
        else:
            drawdown = (self.peak_balance - self.current_balance) / self.peak_balance
            if drawdown > self.max_drawdown:
                self.max_drawdown = drawdown
        
        # Record trade
        trade_record = {
            'symbol': symbol,
            'side': position.side,
            'size': position.size,
            'entry_price': position.entry_price,
            'exit_price': exit_price,
            'leverage': position.leverage,
            'pnl_usd': total_pnl,
            'pnl_percentage': pnl_percentage,
            'duration': (datetime.now() - position.timestamp).total_seconds() / 60,  # minutes
            'timestamp': datetime.now()
        }
        
        self.trade_history.append(trade_record)
        
        # Remove position
        del self.positions[symbol]
        
        logger.info(f"[PORTFOLIO] Closed {position.side.upper()} position: {position.size:.2f} {symbol} @ ${exit_price:.4f}")
        logger.info(f"[PORTFOLIO] PnL: ${total_pnl:.2f} ({pnl_percentage:.1f}%) | Balance: ${self.current_balance:.2f}")
        
        return trade_record
    
    async def update_positions(self, price_updates: Dict[str, float]):
        """Update all positions with current prices"""
        for symbol, current_price in price_updates.items():
            if symbol in self.positions:
                position = self.positions[symbol]
                position.current_price = current_price
                
                # Calculate unrealized PnL
                if position.side == 'long':
                    pnl_per_unit = current_price - position.entry_price
                else:
                    pnl_per_unit = position.entry_price - current_price
                
                position.unrealized_pnl = pnl_per_unit * position.size * position.leverage
                
                # Check stop loss and take profit
                await self.check_risk_levels(symbol, current_price)
    
    async def check_risk_levels(self, symbol: str, current_price: float):
        """Check if position should be closed due to stop loss or take profit"""
        if symbol not in self.positions:
            return
        
        position = self.positions[symbol]
        should_close = False
        close_reason = ""
        
        # Check stop loss
        if position.stop_loss:
            if position.side == 'long' and current_price <= position.stop_loss:
                should_close = True
                close_reason = "Stop loss triggered"
            elif position.side == 'short' and current_price >= position.stop_loss:
                should_close = True
                close_reason = "Stop loss triggered"
        
        # Check take profit
        if position.take_profit and not should_close:
            if position.side == 'long' and current_price >= position.take_profit:
                should_close = True
                close_reason = "Take profit triggered"
            elif position.side == 'short' and current_price <= position.take_profit:
                should_close = True
                close_reason = "Take profit triggered"
        
        if should_close:
            logger.info(f"[PORTFOLIO] {close_reason} for {symbol}")
            await self.close_position(symbol, current_price)
    
    async def rebalance_portfolio(self):
        """Rebalance portfolio if needed"""
        # Check for risk management triggers
        current_risk = self.calculate_total_portfolio_risk()
        
        if current_risk > self.max_portfolio_risk * 1.2:  # 20% over limit
            logger.warning(f"[PORTFOLIO] Risk {current_risk:.1%} exceeds threshold. Rebalancing...")
            
            # Close most risky positions first
            risk_positions = []
            for symbol, position in self.positions.items():
                position_value = position.size * position.current_price
                risk = (position_value * position.leverage) / self.current_balance
                risk_positions.append((symbol, risk, position.unrealized_pnl))
            
            # Sort by risk descending, then by PnL ascending (close losing positions first)
            risk_positions.sort(key=lambda x: (-x[1], x[2]))
            
            # Close positions until risk is acceptable
            for symbol, risk, pnl in risk_positions:
                if self.calculate_total_portfolio_risk() <= self.max_portfolio_risk:
                    break
                
                await self.close_position(symbol, self.positions[symbol].current_price)
                logger.info(f"[PORTFOLIO] Closed {symbol} for risk management (risk: {risk:.1%})")
    
    def get_portfolio_summary(self) -> Dict:
        """Get current portfolio summary"""
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        total_value = self.current_balance + total_unrealized_pnl
        
        return {
            'balance': self.current_balance,
            'total_value': total_value,
            'unrealized_pnl': total_unrealized_pnl,
            'daily_pnl': self.daily_pnl,
            'max_drawdown': self.max_drawdown,
            'total_return': (total_value - self.initial_balance) / self.initial_balance,
            'open_positions': len(self.positions),
            'portfolio_risk': self.calculate_total_portfolio_risk(),
            'positions': {symbol: {
                'side': pos.side,
                'size': pos.size,
                'entry_price': pos.entry_price,
                'current_price': pos.current_price,
                'unrealized_pnl': pos.unrealized_pnl,
                'leverage': pos.leverage
            } for symbol, pos in self.positions.items()}
        }
    
    def reset_daily_pnl(self):
        """Reset daily PnL (call at start of each day)"""
        self.daily_pnl = 0.0
        logger.info("[PORTFOLIO] Daily PnL reset")
    
    def set_risk_limits(self, max_portfolio_risk: float = None, max_position_size: float = None, 
                       max_leverage: float = None, max_daily_loss: float = None):
        """Update risk management limits"""
        if max_portfolio_risk is not None:
            self.max_portfolio_risk = max_portfolio_risk
        if max_position_size is not None:
            self.max_position_size = max_position_size
        if max_leverage is not None:
            self.max_leverage = max_leverage
        if max_daily_loss is not None:
            self.max_daily_loss = max_daily_loss
        
        logger.info(f"[PORTFOLIO] Risk limits updated: Portfolio: {self.max_portfolio_risk:.1%}, "
                   f"Position: {self.max_position_size:.1%}, Leverage: {self.max_leverage:.1f}x, "
                   f"Daily Loss: {self.max_daily_loss:.1%}")
