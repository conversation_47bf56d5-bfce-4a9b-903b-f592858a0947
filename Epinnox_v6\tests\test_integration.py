"""
Integration tests for the Epinnox Trading System.
These tests verify the end-to-end workflow of the system.
"""
import unittest
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Import the modules to test
from core.main import process_market_data
from core.multi_timeframe import MultiTimeframeAnalyzer
from core.signal_scoring import SignalScorer
from core.features import extract_features
from data.exchange import ExchangeDataFetcher
from trading.simulation_trader import SimulatedTrader
from utils.config import load_config

class TestIntegration(unittest.TestCase):
    """Integration tests for the Epinnox Trading System."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Load configuration
        self.config = load_config()
        
        # Create a test cache directory
        self.test_cache_dir = 'test_cache'
        if not os.path.exists(self.test_cache_dir):
            os.makedirs(self.test_cache_dir)
        
        # Initialize the ExchangeDataFetcher
        self.data_fetcher = ExchangeDataFetcher(
            exchange_id='htx',
            cache_dir=self.test_cache_dir
        )
        
        # Create sample data
        self.sample_data = self._create_sample_data()
        
        # Initialize the MultiTimeframeAnalyzer
        self.mt_analyzer = MultiTimeframeAnalyzer(
            timeframes=['1m', '5m', '15m']
        )
        
        # Initialize the SignalScorer
        self.signal_scorer = SignalScorer(
            smoothing_periods=3,
            use_longer_timeframe=True
        )
        
        # Initialize the SimulatedTrader
        self.simulation_trader = SimulatedTrader(
            initial_balance=10000.0,
            leverage=5
        )
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Clean up the test cache directory
        if os.path.exists(self.test_cache_dir):
            for file in os.listdir(self.test_cache_dir):
                os.remove(os.path.join(self.test_cache_dir, file))
            os.rmdir(self.test_cache_dir)
    
    def _create_sample_data(self):
        """Create sample market data for testing."""
        # Create a date range
        dates = [datetime.now() - timedelta(minutes=i) for i in range(200, 0, -1)]
        
        # Create a sine wave for prices
        t = np.linspace(0, 4*np.pi, 200)
        close_prices = 100 + 10 * np.sin(t)
        
        # Create OHLCV data
        ohlcv_data = {
            'timestamp': dates,
            'open': close_prices - np.random.rand(200),
            'high': close_prices + np.random.rand(200) * 2,
            'low': close_prices - np.random.rand(200) * 2,
            'close': close_prices,
            'volume': np.random.rand(200) * 1000
        }
        
        # Create DataFrame
        df = pd.DataFrame(ohlcv_data)
        
        # Create multi-timeframe data
        multi_tf_data = {
            '1m': df.copy(),
            '5m': df.iloc[::5, :].copy(),  # Sample every 5th row for 5m data
            '15m': df.iloc[::15, :].copy()  # Sample every 15th row for 15m data
        }
        
        # Create order book data
        order_book = {
            'bids': [[99.5, 1.0], [99.0, 2.0], [98.5, 3.0]],
            'asks': [[100.5, 1.0], [101.0, 2.0], [101.5, 3.0]],
            'timestamp': datetime.now().timestamp() * 1000,
            'datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'symbol': 'BTC/USDT',
            'market_type': 'spot'
        }
        
        # Create combined data
        combined_data = {
            'timestamp': datetime.now().timestamp() * 1000,
            'datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'spot_symbol': 'BTC/USDT',
            'futures_symbol': 'BTC/USDT:USDT',
            'spot_ohlcv': df.to_dict('records'),
            'futures_ohlcv': df.to_dict('records'),
            'spot_orderbook': order_book,
            'futures_orderbook': order_book,
            'futures_premium_pct': 0.1,
            'mode': 'historical'
        }
        
        return {
            'multi_timeframe_data': multi_tf_data,
            'combined_data': combined_data
        }
    
    def test_end_to_end_workflow(self):
        """Test the end-to-end workflow of the system."""
        # Get sample data
        multi_timeframe_data = self.sample_data['multi_timeframe_data']
        
        # Process market data using the correct API
        df_1m = multi_timeframe_data['1m']
        result = process_market_data(
            ohlcv_data=df_1m,
            symbol='BTC/USDT',
            config={
                'timeframes': ['1m', '5m', '15m'],
                'signal_weights': {
                    'macd': 0.3,
                    'orderbook': 0.2,
                    'volume': 0.2,
                    'price_action': 0.3
                }
            }
        )
        
        # Check that the result contains the expected fields
        self.assertIn('timestamp', result)
        self.assertIn('success', result)
        self.assertIn('symbol', result)
        self.assertIn('interval', result)
        self.assertIn('price', result)
        self.assertIn('signal', result)
        self.assertIn('confidence', result)
        self.assertIn('alignment', result)
        self.assertIn('explanation', result)
        self.assertIn('timeframe_data', result)
        
        # Check that the signal is a valid value
        self.assertIn(result['signal'], ['BUY', 'SELL', 'WAIT', 'CLOSE'])
        
        # Check that the confidence is within valid range (0-100)
        self.assertTrue(result['confidence'] >= 0)
        self.assertTrue(result['confidence'] <= 100)
        
        # Check that the alignment is within valid range (0-100)
        self.assertTrue(result['alignment'] >= 0)
        self.assertTrue(result['alignment'] <= 100)
        
        # Check that the explanation is not empty
        self.assertTrue(len(result['explanation']) > 0)
        
        # Check that the timeframe data contains the expected fields
        self.assertIn('trend_metrics', result['timeframe_data'])
        self.assertIn('timeframe_analysis', result['timeframe_data'])
    
    def test_simulation_trader(self):
        """Test the SimulationTrader class."""
        # Get sample data
        multi_timeframe_data = self.sample_data['multi_timeframe_data']
        
        # Extract features from the 1m data
        df_1m = multi_timeframe_data['1m'].copy()
        extract_features(df_1m)
        
        # Calculate signal scores (test that it works)
        self.signal_scorer.calculate_scores(df_1m)
        
        # Process the signal by opening a position manually
        # (SimulatedTrader doesn't have process_signal method)
        position_id = self.simulation_trader.open_position(
            symbol='BTC/USDT:USDT',
            side='LONG',
            amount=0.1,
            price=50000.0,
            leverage=2.0
        )

        # Check that a position was opened
        open_positions = self.simulation_trader.get_open_positions()
        self.assertTrue(len(open_positions) > 0)

        # Check that the position has the expected properties
        position = open_positions[position_id]
        self.assertEqual(position['symbol'], 'BTC/USDT:USDT')
        self.assertEqual(position['side'], 'LONG')
        self.assertEqual(position['entry_price'], 50000.0)
        
        # Close the position manually
        self.simulation_trader.close_position(position_id, 51000.0)
        
        # Check that the position was closed
        self.assertFalse(self.simulation_trader.has_open_position())
        
        # Check that the position history was updated
        self.assertEqual(len(self.simulation_trader.position_history), 1)
        
        # Check that the closed position has the expected properties
        closed_position = self.simulation_trader.position_history[0]
        self.assertEqual(closed_position['symbol'], 'BTC/USDT:USDT')
        self.assertEqual(closed_position['side'], 'LONG')
        self.assertEqual(closed_position['entry_price'], 100.0)
        self.assertEqual(closed_position['exit_price'], 110.0)
        self.assertTrue(closed_position['profit'] > 0)
        self.assertEqual(closed_position['status'], 'closed')

if __name__ == '__main__':
    unittest.main()
