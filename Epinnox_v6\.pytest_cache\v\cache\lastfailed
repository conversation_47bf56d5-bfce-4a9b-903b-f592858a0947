{"tests/test_llm_orchestrator_harness.py::test_outside_action_zone_waits": true, "tests/test_llm_orchestrator_harness.py::test_enters_action_zone_triggers_llm": true, "tests/test_llm_orchestrator_harness.py::test_llm_decision_aggregation[responses0-LONG]": true, "tests/test_llm_orchestrator_harness.py::test_llm_decision_aggregation[responses1-SHORT]": true, "tests/test_llm_orchestrator_harness.py::test_cache_growth_and_teardown": true, "tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_full_trading_cycle": true, "tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_risk_management_integration": true, "tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_portfolio_position_updates": true, "tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_stop_loss_integration": true, "tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_decision_to_execution_flow": true, "tests/test_live_data.py::test_live_data": true, "tests/test_reorganized_ui.py::test_reorganized_ui": true, "tests/test_portfolio_manager.py::TestPortfolioManager::test_open_position_success": true, "tests/test_portfolio_manager.py::TestPortfolioManager::test_open_position_switch_sides": true, "tests/test_portfolio_manager.py::TestPortfolioManager::test_close_position_long_profit": true, "tests/test_portfolio_manager.py::TestPortfolioManager::test_close_position_short_profit": true, "tests/test_portfolio_manager.py::TestPortfolioManager::test_close_position_loss": true, "tests/test_portfolio_manager.py::TestPortfolioManager::test_update_positions": true, "tests/test_portfolio_manager.py::TestPortfolioManager::test_check_risk_levels_stop_loss": true, "tests/test_portfolio_manager.py::TestPortfolioManager::test_check_risk_levels_take_profit": true, "tests/models/test_model_loader.py::test_phi2": true, "tests/models/test_model_loader.py::test_tinyllama": true, "tests/models/test_transformers_runner.py::test_transformers_models": true}