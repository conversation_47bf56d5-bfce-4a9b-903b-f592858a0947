#!/usr/bin/env python3
"""
Emergency Stop Procedures Testing
Tests emergency stop mechanisms, circuit breakers, and fail-safe procedures
"""

import sys
import os
import asyncio
import json
from datetime import datetime
import logging
import time

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from portfolio.portfolio_manager import PortfolioManager
from execution.autonomous_executor import AutonomousTradeExecutor
from tests.mocks.mock_exchange import MockExchange
from monitoring.performance_tracker import PerformanceTracker

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EmergencyStopTester:
    """Test emergency stop procedures and circuit breakers"""
    
    def __init__(self):
        self.results = []
        self.mock_exchange = None
        self.portfolio = None
        self.executor = None
        self.performance_tracker = None
    
    async def setup_test_environment(self, initial_balance=10000.0):
        """Setup test environment with positions"""
        logger.info(f"Setting up emergency stop test environment (${initial_balance:,.2f})")
        
        # Create mock exchange
        self.mock_exchange = MockExchange(initial_balance=initial_balance)
        
        # Create portfolio manager
        self.portfolio = PortfolioManager(initial_balance=initial_balance, max_positions=5)
        
        # Create autonomous executor
        self.executor = AutonomousTradeExecutor(self.mock_exchange)
        
        # Create performance tracker
        self.performance_tracker = PerformanceTracker()
        
        logger.info("✅ Emergency stop test environment setup complete")
    
    async def create_test_positions(self):
        """Create multiple test positions for emergency stop testing"""
        logger.info("Creating test positions for emergency stop testing...")
        
        # Create multiple positions
        positions = [
            ('BTC/USDT', 'long', 0.01, 50000.0, 1.0),
            ('ETH/USDT', 'short', 0.5, 3000.0, 1.0),
            ('DOGE/USDT', 'long', 1000.0, 0.1, 1.0)
        ]
        
        for symbol, side, size, price, leverage in positions:
            try:
                await self.portfolio.open_position(symbol, side, size, price, leverage)
                logger.info(f"✅ Created {side} position: {size} {symbol} @ ${price}")
            except Exception as e:
                logger.warning(f"⚠️ Could not create position {symbol}: {e}")
        
        logger.info(f"Created {len(self.portfolio.positions)} test positions")
        return len(self.portfolio.positions)
    
    async def test_manual_emergency_stop(self):
        """Test manual emergency stop functionality"""
        logger.info("🔧 Testing Manual Emergency Stop")
        
        try:
            # Setup environment and create positions
            await self.setup_test_environment(10000.0)
            positions_created = await self.create_test_positions()
            
            # Record initial state
            initial_positions = len(self.portfolio.positions)
            initial_balance = self.portfolio.current_balance
            
            # Trigger manual emergency stop
            start_time = time.time()
            closed_positions = await self.portfolio.close_all_positions("Manual emergency stop test")
            stop_time = time.time()
            
            # Validate results
            final_positions = len(self.portfolio.positions)
            execution_time = stop_time - start_time
            
            result = {
                'test': 'Manual Emergency Stop',
                'initial_positions': initial_positions,
                'final_positions': final_positions,
                'positions_closed': len(closed_positions),
                'execution_time_seconds': execution_time,
                'all_positions_closed': final_positions == 0,
                'success': final_positions == 0 and execution_time < 5.0  # Should complete within 5 seconds
            }
            
            self.results.append(result)
            logger.info(f"✅ Manual emergency stop: {len(closed_positions)} positions closed in {execution_time:.2f}s")
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Manual emergency stop test failed: {e}")
            return False
    
    async def test_daily_loss_circuit_breaker(self):
        """Test daily loss circuit breaker"""
        logger.info("🔧 Testing Daily Loss Circuit Breaker")
        
        try:
            # Setup environment with smaller balance for testing
            await self.setup_test_environment(1000.0)
            
            # Set aggressive daily loss limit for testing
            self.portfolio.max_daily_loss = 0.10  # 10% daily loss limit
            
            # Simulate large loss
            self.portfolio.daily_pnl = -120.0  # 12% loss (exceeds 10% limit)
            
            # Try to open new position (should be blocked)
            try:
                await self.portfolio.open_position('BTC/USDT', 'long', 0.001, 50000.0, 1.0)
                circuit_breaker_triggered = False
                logger.warning("⚠️ Position was allowed despite daily loss limit")
            except Exception as e:
                circuit_breaker_triggered = True
                logger.info(f"✅ Circuit breaker triggered: {e}")
            
            result = {
                'test': 'Daily Loss Circuit Breaker',
                'daily_loss': self.portfolio.daily_pnl,
                'daily_loss_limit': -self.portfolio.current_balance * self.portfolio.max_daily_loss,
                'circuit_breaker_triggered': circuit_breaker_triggered,
                'success': circuit_breaker_triggered
            }
            
            self.results.append(result)
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Daily loss circuit breaker test failed: {e}")
            return False
    
    async def test_portfolio_risk_circuit_breaker(self):
        """Test portfolio risk circuit breaker"""
        logger.info("🔧 Testing Portfolio Risk Circuit Breaker")
        
        try:
            # Setup environment
            await self.setup_test_environment(5000.0)
            
            # Create positions that approach risk limit
            await self.portfolio.open_position('BTC/USDT', 'long', 0.005, 50000.0, 2.0)  # ~10% risk
            await self.portfolio.open_position('ETH/USDT', 'short', 0.25, 3000.0, 1.5)   # ~11.25% risk
            
            current_risk = self.portfolio.calculate_total_portfolio_risk()
            logger.info(f"Current portfolio risk: {current_risk:.1%}")
            
            # Try to add position that would exceed risk limit
            try:
                await self.portfolio.open_position('DOGE/USDT', 'long', 500.0, 0.1, 1.0)  # Would add ~1% risk
                risk_breaker_triggered = False
                logger.warning("⚠️ Position was allowed despite risk limit")
            except Exception as e:
                risk_breaker_triggered = True
                logger.info(f"✅ Risk circuit breaker triggered: {e}")
            
            result = {
                'test': 'Portfolio Risk Circuit Breaker',
                'current_risk': current_risk,
                'max_risk_limit': self.portfolio.max_portfolio_risk,
                'risk_breaker_triggered': risk_breaker_triggered,
                'success': risk_breaker_triggered
            }
            
            self.results.append(result)
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Portfolio risk circuit breaker test failed: {e}")
            return False
    
    async def test_rapid_loss_detection(self):
        """Test rapid loss detection and emergency response"""
        logger.info("🔧 Testing Rapid Loss Detection")
        
        try:
            # Setup environment and create positions
            await self.setup_test_environment(10000.0)
            await self.create_test_positions()
            
            # Simulate rapid market crash
            crash_prices = {
                'BTC/USDT': 40000.0,  # -20% from 50000
                'ETH/USDT': 3600.0,   # +20% (bad for short position)
                'DOGE/USDT': 0.07     # -30% from 0.1
            }
            
            # Update positions with crash prices
            await self.portfolio.update_positions(crash_prices)
            
            # Calculate total unrealized loss
            total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.portfolio.positions.values())
            loss_percentage = abs(total_unrealized_pnl) / self.portfolio.current_balance
            
            # Check if rapid loss would trigger emergency stop
            rapid_loss_threshold = 0.15  # 15% rapid loss threshold
            should_trigger_emergency = loss_percentage > rapid_loss_threshold
            
            if should_trigger_emergency:
                # Simulate emergency stop
                closed_positions = await self.portfolio.close_all_positions("Rapid loss detected")
                emergency_triggered = True
                logger.info(f"✅ Emergency stop triggered for {loss_percentage:.1%} loss")
            else:
                emergency_triggered = False
                closed_positions = []
                logger.info(f"Loss {loss_percentage:.1%} within acceptable range")
            
            result = {
                'test': 'Rapid Loss Detection',
                'total_unrealized_loss': total_unrealized_pnl,
                'loss_percentage': loss_percentage,
                'rapid_loss_threshold': rapid_loss_threshold,
                'emergency_triggered': emergency_triggered,
                'positions_closed': len(closed_positions),
                'success': should_trigger_emergency == emergency_triggered
            }
            
            self.results.append(result)
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Rapid loss detection test failed: {e}")
            return False
    
    async def test_system_failure_recovery(self):
        """Test system failure recovery procedures"""
        logger.info("🔧 Testing System Failure Recovery")
        
        try:
            # Setup environment and create positions
            await self.setup_test_environment(10000.0)
            await self.create_test_positions()
            
            initial_positions = len(self.portfolio.positions)
            
            # Simulate system failure by corrupting exchange connection
            original_exchange = self.portfolio
            
            # Test graceful degradation
            try:
                # Attempt to close positions despite "system failure"
                closed_positions = await self.portfolio.close_all_positions("System failure recovery test")
                recovery_successful = True
                logger.info("✅ System failure recovery successful")
            except Exception as e:
                recovery_successful = False
                closed_positions = []
                logger.error(f"❌ System failure recovery failed: {e}")
            
            result = {
                'test': 'System Failure Recovery',
                'initial_positions': initial_positions,
                'recovery_successful': recovery_successful,
                'positions_closed': len(closed_positions),
                'success': recovery_successful
            }
            
            self.results.append(result)
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ System failure recovery test failed: {e}")
            return False
    
    def generate_emergency_stop_report(self):
        """Generate comprehensive emergency stop testing report"""
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'total_tests': len(self.results),
            'passed_tests': sum(1 for r in self.results if r['success']),
            'failed_tests': sum(1 for r in self.results if not r['success']),
            'success_rate': sum(1 for r in self.results if r['success']) / len(self.results) * 100 if self.results else 0,
            'emergency_stop_tests': self.results
        }
        
        # Save report
        filename = f"emergency_stop_procedures_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        return report, filename

async def main():
    """Run all emergency stop procedure tests"""
    print("🚀 Starting Emergency Stop Procedures Testing")
    print("=" * 60)
    
    tester = EmergencyStopTester()
    
    # Run all emergency stop tests
    tests = [
        tester.test_manual_emergency_stop,
        tester.test_daily_loss_circuit_breaker,
        tester.test_portfolio_risk_circuit_breaker,
        tester.test_rapid_loss_detection,
        tester.test_system_failure_recovery,
    ]
    
    results = []
    for i, test in enumerate(tests, 1):
        print(f"\n🔧 Running emergency stop test {i}/{len(tests)}...")
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Emergency stop test {i} failed with exception: {e}")
            results.append(False)
    
    # Generate report
    report, filename = tester.generate_emergency_stop_report()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 EMERGENCY STOP PROCEDURES SUMMARY")
    print("=" * 60)
    
    for i, (test_result, success) in enumerate(zip(tester.results, results), 1):
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - Test {i}: {test_result['test']}")
    
    print(f"\nResults: {report['passed_tests']}/{report['total_tests']} tests passed")
    print(f"Success Rate: {report['success_rate']:.1f}%")
    print(f"📄 Detailed report saved to: {filename}")
    
    if report['success_rate'] >= 80:
        print("🎉 Emergency stop procedures testing PASSED!")
        return True
    else:
        print("⚠️ Some emergency stop tests failed. Review the detailed report.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
