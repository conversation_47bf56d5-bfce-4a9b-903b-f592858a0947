#!/usr/bin/env python3
"""
Conservative Live Trading Configuration
Configure ultra-conservative settings for initial live deployment
"""

import sys
import os
import json
import time
from datetime import datetime
import logging

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConservativeLiveTradingConfigurator:
    """Configure conservative settings for live trading deployment"""
    
    def __init__(self):
        self.config = {}
        self.validation_results = []
    
    def create_conservative_config(self):
        """Create ultra-conservative configuration for live trading"""
        logger.info("🔧 Creating conservative live trading configuration...")
        
        self.config = {
            "system_info": {
                "config_version": "1.0.0",
                "created_timestamp": datetime.now().isoformat(),
                "deployment_type": "conservative_live",
                "description": "Ultra-conservative configuration for initial live trading deployment"
            },
            
            "risk_management": {
                "max_portfolio_risk": 0.02,      # 2% maximum portfolio risk
                "max_position_size": 0.01,       # 1% maximum position size
                "max_leverage": 1.0,             # No leverage (1x only)
                "max_daily_loss": 0.005,         # 0.5% maximum daily loss
                "max_concurrent_positions": 1,   # Only 1 position at a time
                "position_timeout_hours": 24,    # Close positions after 24 hours
                "stop_loss_percentage": 0.02,    # 2% stop loss
                "take_profit_percentage": 0.04   # 4% take profit (2:1 ratio)
            },
            
            "trading_parameters": {
                "initial_balance": 100.0,        # Start with $100 only
                "minimum_confidence": 0.80,      # 80% minimum confidence for trades
                "trading_symbols": ["BTC/USDT:USDT"],  # Only BTC initially
                "trading_hours": {
                    "enabled": True,
                    "start_hour": 9,             # 9 AM
                    "end_hour": 17               # 5 PM (market hours only)
                },
                "cooldown_period_minutes": 60,   # 1 hour between trades
                "max_trades_per_day": 2          # Maximum 2 trades per day
            },
            
            "ai_model_settings": {
                "rl_agent": {
                    "enabled": True,
                    "confidence_threshold": 0.75,
                    "exploration_rate": 0.05     # Very low exploration
                },
                "llm_analysis": {
                    "enabled": True,
                    "minimum_confidence": 0.80,
                    "sentiment_weight": 0.3,
                    "technical_weight": 0.7
                },
                "decision_aggregation": {
                    "require_consensus": True,    # Both AI and LLM must agree
                    "minimum_combined_confidence": 0.85
                }
            },
            
            "monitoring_and_alerts": {
                "real_time_monitoring": {
                    "enabled": True,
                    "update_interval_seconds": 30,
                    "log_all_decisions": True
                },
                "alert_thresholds": {
                    "portfolio_loss_percent": 0.5,   # Alert at 0.5% loss
                    "position_loss_percent": 1.0,    # Alert at 1% position loss
                    "system_error_count": 3,         # Alert after 3 errors
                    "api_failure_count": 2           # Alert after 2 API failures
                },
                "notification_methods": {
                    "console_logging": True,
                    "file_logging": True,
                    "email_alerts": False,           # Disabled for now
                    "webhook_alerts": False          # Disabled for now
                }
            },
            
            "safety_mechanisms": {
                "circuit_breakers": {
                    "daily_loss_limit": 0.005,      # 0.5% daily loss circuit breaker
                    "consecutive_losses": 3,         # Stop after 3 consecutive losses
                    "api_error_limit": 5,            # Stop after 5 API errors
                    "system_error_limit": 3          # Stop after 3 system errors
                },
                "emergency_stop": {
                    "enabled": True,
                    "manual_override": True,
                    "auto_stop_conditions": [
                        "daily_loss_exceeded",
                        "consecutive_losses_exceeded",
                        "system_errors_exceeded"
                    ]
                },
                "position_management": {
                    "force_close_on_loss": True,
                    "force_close_percentage": 2.0,   # Force close at 2% loss
                    "trailing_stop_enabled": False,  # Disabled for simplicity
                    "partial_close_enabled": False   # Disabled for simplicity
                }
            },
            
            "exchange_settings": {
                "primary_exchange": "htx",
                "demo_mode": False,              # Live trading mode
                "api_rate_limits": {
                    "requests_per_minute": 30,   # Conservative API usage
                    "orders_per_minute": 5       # Very limited order frequency
                },
                "order_types": {
                    "market_orders": True,
                    "limit_orders": False,       # Disabled for simplicity
                    "stop_orders": True,
                    "advanced_orders": False     # Disabled for safety
                }
            },
            
            "logging_and_reporting": {
                "detailed_logging": True,
                "log_level": "INFO",
                "log_file_rotation": True,
                "daily_reports": True,
                "trade_history_backup": True,
                "performance_metrics": {
                    "track_pnl": True,
                    "track_drawdown": True,
                    "track_win_rate": True,
                    "track_sharpe_ratio": True
                }
            }
        }
        
        logger.info("✅ Conservative live trading configuration created")
        return self.config
    
    def validate_configuration(self):
        """Validate the conservative configuration"""
        logger.info("🔧 Validating conservative configuration...")
        
        validations = []
        
        # Validate risk management settings
        risk_config = self.config.get("risk_management", {})
        validations.append({
            "category": "Risk Management",
            "check": "Portfolio risk <= 2%",
            "value": risk_config.get("max_portfolio_risk", 0),
            "valid": risk_config.get("max_portfolio_risk", 0) <= 0.02,
            "critical": True
        })
        
        validations.append({
            "category": "Risk Management", 
            "check": "Position size <= 1%",
            "value": risk_config.get("max_position_size", 0),
            "valid": risk_config.get("max_position_size", 0) <= 0.01,
            "critical": True
        })
        
        validations.append({
            "category": "Risk Management",
            "check": "No leverage (1x only)",
            "value": risk_config.get("max_leverage", 0),
            "valid": risk_config.get("max_leverage", 0) <= 1.0,
            "critical": True
        })
        
        # Validate trading parameters
        trading_config = self.config.get("trading_parameters", {})
        validations.append({
            "category": "Trading Parameters",
            "check": "Small initial balance",
            "value": trading_config.get("initial_balance", 0),
            "valid": trading_config.get("initial_balance", 0) <= 1000.0,
            "critical": True
        })
        
        validations.append({
            "category": "Trading Parameters",
            "check": "High confidence threshold",
            "value": trading_config.get("minimum_confidence", 0),
            "valid": trading_config.get("minimum_confidence", 0) >= 0.75,
            "critical": True
        })
        
        validations.append({
            "category": "Trading Parameters",
            "check": "Limited concurrent positions",
            "value": risk_config.get("max_concurrent_positions", 0),
            "valid": risk_config.get("max_concurrent_positions", 0) <= 2,
            "critical": True
        })
        
        # Validate safety mechanisms
        safety_config = self.config.get("safety_mechanisms", {})
        circuit_breakers = safety_config.get("circuit_breakers", {})
        validations.append({
            "category": "Safety Mechanisms",
            "check": "Circuit breakers enabled",
            "value": len(circuit_breakers),
            "valid": len(circuit_breakers) >= 3,
            "critical": True
        })
        
        emergency_stop = safety_config.get("emergency_stop", {})
        validations.append({
            "category": "Safety Mechanisms",
            "check": "Emergency stop enabled",
            "value": emergency_stop.get("enabled", False),
            "valid": emergency_stop.get("enabled", False) == True,
            "critical": True
        })
        
        # Validate monitoring
        monitoring_config = self.config.get("monitoring_and_alerts", {})
        validations.append({
            "category": "Monitoring",
            "check": "Real-time monitoring enabled",
            "value": monitoring_config.get("real_time_monitoring", {}).get("enabled", False),
            "valid": monitoring_config.get("real_time_monitoring", {}).get("enabled", False) == True,
            "critical": False
        })
        
        # Calculate validation results
        total_validations = len(validations)
        passed_validations = sum(1 for v in validations if v["valid"])
        critical_validations = [v for v in validations if v.get("critical", False)]
        passed_critical = sum(1 for v in critical_validations if v["valid"])
        
        validation_summary = {
            "total_validations": total_validations,
            "passed_validations": passed_validations,
            "critical_validations": len(critical_validations),
            "passed_critical": passed_critical,
            "validation_details": validations,
            "overall_valid": passed_critical == len(critical_validations),
            "success_rate": passed_validations / total_validations * 100
        }
        
        self.validation_results = validation_summary
        
        logger.info(f"✅ Configuration validation: {passed_validations}/{total_validations} checks passed")
        logger.info(f"✅ Critical validations: {passed_critical}/{len(critical_validations)} passed")
        
        return validation_summary["overall_valid"]
    
    def generate_deployment_checklist(self):
        """Generate deployment checklist for live trading"""
        logger.info("📋 Generating deployment checklist...")
        
        checklist = {
            "pre_deployment": [
                "✅ Conservative configuration validated",
                "✅ All safety mechanisms tested",
                "✅ Risk limits properly configured",
                "✅ Emergency stop procedures verified",
                "⚠️ API credentials configured (manual step)",
                "⚠️ Exchange account funded with small amount (manual step)",
                "⚠️ Monitoring systems activated (manual step)"
            ],
            "deployment_steps": [
                "1. Start with paper trading mode for final validation",
                "2. Switch to live mode with $100 initial balance",
                "3. Monitor first trade execution closely",
                "4. Verify all safety mechanisms are active",
                "5. Gradually increase balance if system performs well"
            ],
            "post_deployment": [
                "Monitor system performance for first 24 hours",
                "Review all trade decisions and outcomes",
                "Validate risk management effectiveness",
                "Check alert system functionality",
                "Document any issues or improvements needed"
            ],
            "emergency_procedures": [
                "Manual emergency stop: Stop all trading immediately",
                "Close all positions: Liquidate all open positions",
                "System shutdown: Disable autonomous trading",
                "Review logs: Analyze what triggered emergency",
                "Contact support: If needed for technical issues"
            ]
        }
        
        return checklist
    
    def save_configuration(self):
        """Save the conservative configuration to file"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        config_filename = f"conservative_live_trading_config_{timestamp}.json"
        
        # Save main configuration
        with open(config_filename, 'w') as f:
            json.dump(self.config, f, indent=2)
        
        # Save validation results
        validation_filename = f"config_validation_results_{timestamp}.json"
        with open(validation_filename, 'w') as f:
            json.dump(self.validation_results, f, indent=2)
        
        # Save deployment checklist
        checklist = self.generate_deployment_checklist()
        checklist_filename = f"deployment_checklist_{timestamp}.json"
        with open(checklist_filename, 'w') as f:
            json.dump(checklist, f, indent=2)
        
        logger.info(f"✅ Configuration saved to: {config_filename}")
        logger.info(f"✅ Validation results saved to: {validation_filename}")
        logger.info(f"✅ Deployment checklist saved to: {checklist_filename}")
        
        return {
            "config_file": config_filename,
            "validation_file": validation_filename,
            "checklist_file": checklist_filename
        }

def main():
    """Configure conservative live trading settings"""
    print("🚀 Starting Conservative Live Trading Configuration")
    print("=" * 60)
    
    configurator = ConservativeLiveTradingConfigurator()
    
    # Create conservative configuration
    print("\n🔧 Creating conservative configuration...")
    config = configurator.create_conservative_config()
    
    # Validate configuration
    print("\n🔧 Validating configuration...")
    validation_success = configurator.validate_configuration()
    
    # Save configuration files
    print("\n💾 Saving configuration files...")
    saved_files = configurator.save_configuration()
    
    # Generate summary
    print("\n" + "=" * 60)
    print("📋 CONSERVATIVE LIVE TRADING CONFIGURATION SUMMARY")
    print("=" * 60)
    
    print(f"✅ Configuration created: {len(config)} sections")
    print(f"✅ Validation result: {'PASSED' if validation_success else 'FAILED'}")
    print(f"✅ Files saved: {len(saved_files)} files")
    
    print("\n🛡️ KEY SAFETY SETTINGS:")
    risk_config = config.get("risk_management", {})
    print(f"   • Portfolio Risk: {risk_config.get('max_portfolio_risk', 0):.1%}")
    print(f"   • Position Size: {risk_config.get('max_position_size', 0):.1%}")
    print(f"   • Leverage: {risk_config.get('max_leverage', 0):.1f}x")
    print(f"   • Daily Loss Limit: {risk_config.get('max_daily_loss', 0):.1%}")
    print(f"   • Max Positions: {risk_config.get('max_concurrent_positions', 0)}")
    
    trading_config = config.get("trading_parameters", {})
    print(f"   • Initial Balance: ${trading_config.get('initial_balance', 0):.0f}")
    print(f"   • Min Confidence: {trading_config.get('minimum_confidence', 0):.0%}")
    print(f"   • Max Daily Trades: {trading_config.get('max_trades_per_day', 0)}")
    
    print(f"\n📄 Configuration files:")
    for file_type, filename in saved_files.items():
        print(f"   • {file_type}: {filename}")
    
    if validation_success:
        print("\n🎉 Conservative live trading configuration READY for deployment!")
        print("⚠️  IMPORTANT: Review deployment checklist before going live!")
    else:
        print("\n❌ Configuration validation FAILED. Review settings before deployment.")
    
    return validation_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
