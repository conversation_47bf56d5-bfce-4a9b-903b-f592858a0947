#!/usr/bin/env python3
"""
Comprehensive Paper Trading Scenarios Test
Validates order execution, position management, and PnL tracking
"""

import sys
import os
import asyncio
import json
from datetime import datetime, timedelta
import logging

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tests.mocks.mock_exchange import MockExchange
from portfolio.portfolio_manager import PortfolioManager
from execution.autonomous_executor import AutonomousTradeExecutor
from monitoring.performance_tracker import PerformanceTracker

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PaperTradingScenarioTester:
    """Test various paper trading scenarios"""
    
    def __init__(self):
        self.results = []
        self.mock_exchange = None
        self.portfolio = None
        self.executor = None
        self.performance_tracker = None
    
    async def setup_environment(self, initial_balance=10000.0):
        """Setup paper trading environment"""
        logger.info(f"Setting up paper trading environment with ${initial_balance:,.2f}")
        
        # Create mock exchange
        self.mock_exchange = MockExchange(initial_balance=initial_balance)
        
        # Create portfolio manager
        self.portfolio = PortfolioManager(initial_balance=initial_balance)
        
        # Create autonomous executor
        self.executor = AutonomousTradeExecutor(self.mock_exchange)
        
        # Create performance tracker
        self.performance_tracker = PerformanceTracker()
        
        logger.info("✅ Paper trading environment setup complete")
    
    async def scenario_1_basic_long_trade(self):
        """Test basic long position opening and closing"""
        logger.info("🔧 Running Scenario 1: Basic Long Trade")
        
        try:
            # Open long position
            await self.portfolio.open_position('BTC/USDT', 'long', 0.01, 50000.0, 1.0)
            
            # Simulate price increase
            self.mock_exchange.prices['BTC/USDT'] = 52000.0
            
            # Update position
            await self.portfolio.update_positions({'BTC/USDT': 52000.0})
            
            # Close position
            await self.portfolio.close_position('BTC/USDT', 52000.0)
            
            # Validate results
            final_balance = self.portfolio.current_balance
            expected_profit = (52000.0 - 50000.0) * 0.01  # $20 profit
            
            result = {
                'scenario': 'Basic Long Trade',
                'initial_balance': 10000.0,
                'final_balance': final_balance,
                'expected_profit': expected_profit,
                'actual_profit': final_balance - 10000.0,
                'success': abs((final_balance - 10000.0) - expected_profit) < 1.0
            }
            
            self.results.append(result)
            logger.info(f"✅ Scenario 1 completed: Profit ${final_balance - 10000.0:.2f}")
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Scenario 1 failed: {e}")
            return False
    
    async def scenario_2_basic_short_trade(self):
        """Test basic short position opening and closing"""
        logger.info("🔧 Running Scenario 2: Basic Short Trade")
        
        try:
            # Reset environment
            await self.setup_environment(10000.0)
            
            # Open short position
            await self.portfolio.open_position('BTC/USDT', 'short', 0.01, 50000.0, 1.0)
            
            # Simulate price decrease
            self.mock_exchange.prices['BTC/USDT'] = 48000.0
            
            # Update position
            await self.portfolio.update_positions({'BTC/USDT': 48000.0})
            
            # Close position
            await self.portfolio.close_position('BTC/USDT', 48000.0)
            
            # Validate results
            final_balance = self.portfolio.current_balance
            expected_profit = (50000.0 - 48000.0) * 0.01  # $20 profit
            
            result = {
                'scenario': 'Basic Short Trade',
                'initial_balance': 10000.0,
                'final_balance': final_balance,
                'expected_profit': expected_profit,
                'actual_profit': final_balance - 10000.0,
                'success': abs((final_balance - 10000.0) - expected_profit) < 1.0
            }
            
            self.results.append(result)
            logger.info(f"✅ Scenario 2 completed: Profit ${final_balance - 10000.0:.2f}")
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Scenario 2 failed: {e}")
            return False
    
    async def scenario_3_multiple_positions(self):
        """Test multiple concurrent positions"""
        logger.info("🔧 Running Scenario 3: Multiple Positions")
        
        try:
            # Reset environment
            await self.setup_environment(10000.0)
            
            # Open multiple positions
            await self.portfolio.open_position('BTC/USDT', 'long', 0.01, 50000.0, 1.0)
            await self.portfolio.open_position('ETH/USDT', 'short', 0.5, 3000.0, 1.0)
            
            # Simulate price movements
            self.mock_exchange.prices['BTC/USDT'] = 51000.0  # +$10 profit
            self.mock_exchange.prices['ETH/USDT'] = 2950.0   # +$25 profit
            
            # Update positions
            await self.portfolio.update_positions({
                'BTC/USDT': 51000.0,
                'ETH/USDT': 2950.0
            })
            
            # Check portfolio status
            total_positions = len(self.portfolio.positions)
            total_risk = self.portfolio.calculate_total_portfolio_risk()
            
            result = {
                'scenario': 'Multiple Positions',
                'positions_opened': total_positions,
                'portfolio_risk': total_risk,
                'success': total_positions == 2 and total_risk <= 0.20  # Within 20% risk limit
            }
            
            self.results.append(result)
            logger.info(f"✅ Scenario 3 completed: {total_positions} positions, {total_risk:.1%} risk")
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Scenario 3 failed: {e}")
            return False
    
    async def scenario_4_risk_limits(self):
        """Test risk limit enforcement"""
        logger.info("🔧 Running Scenario 4: Risk Limits")
        
        try:
            # Reset environment
            await self.setup_environment(1000.0)  # Smaller balance for risk testing
            
            # Try to open position that exceeds risk limits
            try:
                # This should fail due to portfolio risk limits
                await self.portfolio.open_position('BTC/USDT', 'long', 0.01, 50000.0, 1.0)  # $500 = 50% of balance
                risk_limit_enforced = False
            except Exception:
                risk_limit_enforced = True
            
            result = {
                'scenario': 'Risk Limits',
                'risk_limit_enforced': risk_limit_enforced,
                'success': risk_limit_enforced
            }
            
            self.results.append(result)
            logger.info(f"✅ Scenario 4 completed: Risk limits {'enforced' if risk_limit_enforced else 'NOT enforced'}")
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Scenario 4 failed: {e}")
            return False
    
    async def scenario_5_stop_loss_take_profit(self):
        """Test stop loss and take profit functionality"""
        logger.info("🔧 Running Scenario 5: Stop Loss & Take Profit")
        
        try:
            # Reset environment
            await self.setup_environment(10000.0)
            
            # Open position with stop loss and take profit
            await self.portfolio.open_position('BTC/USDT', 'long', 0.01, 50000.0, 1.0, 
                                             stop_loss=49000.0, take_profit=52000.0)
            
            # Test stop loss trigger
            self.mock_exchange.prices['BTC/USDT'] = 48500.0  # Below stop loss
            
            # Check risk levels
            risk_alerts = await self.portfolio.check_risk_levels({'BTC/USDT': 48500.0})
            
            result = {
                'scenario': 'Stop Loss & Take Profit',
                'stop_loss_triggered': len(risk_alerts) > 0,
                'success': len(risk_alerts) > 0
            }
            
            self.results.append(result)
            logger.info(f"✅ Scenario 5 completed: Stop loss {'triggered' if len(risk_alerts) > 0 else 'NOT triggered'}")
            return result['success']
            
        except Exception as e:
            logger.error(f"❌ Scenario 5 failed: {e}")
            return False
    
    def generate_report(self):
        """Generate comprehensive test report"""
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'total_scenarios': len(self.results),
            'passed_scenarios': sum(1 for r in self.results if r['success']),
            'failed_scenarios': sum(1 for r in self.results if not r['success']),
            'success_rate': sum(1 for r in self.results if r['success']) / len(self.results) * 100,
            'scenarios': self.results
        }
        
        # Save report
        filename = f"paper_trading_scenarios_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        return report, filename

async def main():
    """Run all paper trading scenarios"""
    print("🚀 Starting Paper Trading Scenarios Test")
    print("=" * 60)
    
    tester = PaperTradingScenarioTester()
    
    # Setup initial environment
    await tester.setup_environment()
    
    # Run all scenarios
    scenarios = [
        tester.scenario_1_basic_long_trade,
        tester.scenario_2_basic_short_trade,
        tester.scenario_3_multiple_positions,
        tester.scenario_4_risk_limits,
        tester.scenario_5_stop_loss_take_profit,
    ]
    
    results = []
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n🔧 Running scenario {i}/{len(scenarios)}...")
        try:
            result = await scenario()
            results.append(result)
        except Exception as e:
            print(f"❌ Scenario {i} failed with exception: {e}")
            results.append(False)
    
    # Generate report
    report, filename = tester.generate_report()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 PAPER TRADING SCENARIOS SUMMARY")
    print("=" * 60)
    
    for i, (scenario_result, success) in enumerate(zip(tester.results, results), 1):
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - Scenario {i}: {scenario_result['scenario']}")
    
    print(f"\nResults: {report['passed_scenarios']}/{report['total_scenarios']} scenarios passed")
    print(f"Success Rate: {report['success_rate']:.1f}%")
    print(f"📄 Detailed report saved to: {filename}")
    
    if report['success_rate'] >= 80:
        print("🎉 Paper trading scenarios validation PASSED!")
        return True
    else:
        print("⚠️ Some scenarios failed. Review the detailed report.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
