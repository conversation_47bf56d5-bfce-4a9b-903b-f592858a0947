"""
LLM Prompt Orchestrator - Complete AI Trading System
Manages multiple specialized LLM prompts for comprehensive trading decisions
"""

import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import requests

logger = logging.getLogger(__name__)

ACTION_ZONE_PCT = 0.5
0
def aggregate_signal(signals, weights):
    score = {}
    for sig, w in zip(signals, weights):
        decision = sig['decision']
        if decision not in score:
            score[decision] = 0.0
        score[decision] += sig['confidence'] * w
    return max(score, key=score.get), score

def price_in_action_zone(price, support, resistance):
    low  = support*(1+ACTION_ZONE_PCT/100)
    high = resistance*(1-ACTION_ZONE_PCT/100)
    return low <= price <= high

def schedule_next(self, decision):
    if self.cache and hasattr(self.cache, 'price_history'):
        ph = [p for _, p in self.cache.price_history]
        move_pct = abs(ph[-1]-ph[-2])/ph[-2]*100 if len(ph)>1 else 100
    else:
        move_pct = 100  # Default to high movement if no cache
        
    if move_pct<0.01:
        interval=120
    elif (self.cache and hasattr(self.cache, 'signal_history') and 
          len(self.cache.signal_history)>=3 and 
          all(s['decision']=='WAIT' for s in list(self.cache.signal_history)[-3:])):
        interval=300
    elif (self.cache and hasattr(self.cache, 'price_history') and 
          len(ph)>0 and 
          price_in_action_zone(ph[-1], self.current_support, self.current_resistance)):
        interval=15
    else:
        interval=60
    self.logger.info(f"Next run in {interval}s (last: {decision}).")
    self.schedule_auto_refresh(interval)

class PromptPriority(Enum):
    EMERGENCY = 1
    CRITICAL = 2
    HIGH = 3
    MEDIUM = 4
    LOW = 5

class PromptType(Enum):
    EMERGENCY_RESPONSE = "emergency_response"
    POSITION_MANAGEMENT = "position_management"
    PROFIT_OPTIMIZATION = "profit_optimization"
    MARKET_REGIME = "market_regime"
    RISK_ASSESSMENT = "risk_assessment"
    ENTRY_TIMING = "entry_timing"
    STRATEGY_ADAPTATION = "strategy_adaptation"
    OPPORTUNITY_SCANNER = "opportunity_scanner"

@dataclass
class PromptResult:
    prompt_type: PromptType
    timestamp: datetime
    response: Dict[str, Any]
    confidence: float
    execution_time: float
    success: bool
    error_message: Optional[str] = None

@dataclass
class TradingContext:
    """Complete trading context for LLM prompts"""
    symbol: str
    current_price: float
    account_balance: float
    open_positions: List[Dict]
    market_data: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    emergency_flags: List[str]
    timestamp: datetime

class LLMPromptOrchestrator:
    """
    Master orchestrator for all LLM trading prompts
    Manages priority, timing, and execution of specialized AI trading decisions
    """
    
    def should_execute_prompt(self, prompt_type: PromptType) -> bool:
        """Determine if a prompt should be executed based on timing and conditions"""
        
        # Check timing interval
        last_execution = self.last_execution_times.get(prompt_type)
        if last_execution:
            time_since_last = time.time() - last_execution
            if time_since_last < self.prompt_intervals[prompt_type]:
                return False
        
        # Emergency prompts always execute if emergency conditions exist
        if prompt_type == PromptType.EMERGENCY_RESPONSE:
            return len(self.emergency_flags) > 0
        
        # Position-related prompts only if positions exist
        if prompt_type in [PromptType.POSITION_MANAGEMENT, PromptType.PROFIT_OPTIMIZATION]:
            return self.has_active_positions()
        
        # Market analysis prompts only if not in emergency mode
        if prompt_type in [PromptType.MARKET_REGIME, PromptType.OPPORTUNITY_SCANNER]:
            return not self.emergency_mode
        
        # Entry timing only if we have capacity for new positions
        if prompt_type == PromptType.ENTRY_TIMING:
            return self.has_position_capacity() and not self.emergency_mode
        
        return True
    
    def execute_prompt_cycle(self, trading_context: TradingContext, mode: str = "full") -> Dict[str, PromptResult]:
        """🚀 OPTIMIZED: Execute LLM prompts with selective execution for performance"""

        cycle_results = {}
        start_time = time.time()

        try:
            # Update emergency flags
            self.update_emergency_flags(trading_context)

            # 🚀 PERFORMANCE OPTIMIZATION: Selective prompt execution based on mode
            if mode == "scalping":
                # Fast execution for scalping (3 essential prompts only)
                prompt_sequence = [
                    PromptType.RISK_ASSESSMENT,       # Priority 1: Risk check
                    PromptType.ENTRY_TIMING,          # Priority 2: Entry decisions
                    PromptType.OPPORTUNITY_SCANNER,   # Priority 3: Opportunity identification
                ]
                logger.info("🏃 SCALPING MODE: Executing 3 essential prompts for speed")
                delay_between_prompts = 0.05  # 50ms for scalping
            else:
                # Full analysis mode (all prompts)
                prompt_sequence = [
                    PromptType.EMERGENCY_RESPONSE,    # Priority 1: IMMEDIATE (skip others if action taken)
                    PromptType.MARKET_REGIME,         # Priority 2: Foundation analysis (informs other prompts)
                    PromptType.RISK_ASSESSMENT,       # Priority 3: Risk check before entry decisions
                    PromptType.ENTRY_TIMING,          # Priority 4: Entry decisions (depends on risk)
                    PromptType.OPPORTUNITY_SCANNER,   # Priority 5: Opportunity identification
                    PromptType.POSITION_MANAGEMENT,   # Priority 6: Manage existing positions
                    PromptType.PROFIT_OPTIMIZATION,   # Priority 7: Optimize existing positions
                    PromptType.STRATEGY_ADAPTATION,   # Priority 8: Adapt strategy based on results
                ]
                logger.info("🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis")
                delay_between_prompts = 0.1  # 100ms for full mode

            # Track thread usage for performance monitoring
            thread_usage_start = getattr(self.main_window, 'thread_pool', None)
            if thread_usage_start:
                initial_threads = thread_usage_start.activeThreadCount()
                logger.info(f"📊 LLM Thread Usage Start: {initial_threads}/{thread_usage_start.maxThreadCount()} threads")

            # Execute prompts sequentially with context updates
            for i, prompt_type in enumerate(prompt_sequence):
                if self._should_execute_prompt(prompt_type, trading_context):
                    # Log progress for monitoring
                    logger.info(f"🧠 Executing prompt {i+1}/{len(prompt_sequence)}: {prompt_type.value}")

                    # Execute single prompt
                    result = self._execute_single_prompt(prompt_type, trading_context)
                    cycle_results[prompt_type] = result

                    # 🚀 PERFORMANCE FIX: Update context with previous results for next prompt
                    trading_context = self._update_context_with_result(trading_context, result)

                    # Emergency exit: If emergency action taken, skip remaining prompts
                    if (prompt_type == PromptType.EMERGENCY_RESPONSE and
                        result.success and result.response.get('ACTION') != 'MONITOR'):
                        logger.warning("🚨 Emergency action taken - skipping remaining prompts")
                        break

                    # 🚀 PERFORMANCE FIX: Yield control to prevent UI blocking
                    # Variable delay based on execution mode
                    time.sleep(delay_between_prompts)

            # Update execution times
            for prompt_type in cycle_results.keys():
                self.last_execution_times[prompt_type] = start_time

            # Log cycle completion with performance metrics
            cycle_time = time.time() - start_time
            if thread_usage_start:
                final_threads = thread_usage_start.activeThreadCount()
                logger.info(f"📊 LLM Thread Usage End: {final_threads}/{thread_usage_start.maxThreadCount()} threads")

            logger.info(f"🧠 LLM prompt cycle completed in {cycle_time:.2f}s - {len(cycle_results)} prompts executed sequentially")

            return cycle_results

        except Exception as e:
            logger.error(f"❌ Error in LLM prompt cycle: {e}")
            return cycle_results
    
    def _should_execute_prompt(self, prompt_type: PromptType, trading_context: TradingContext) -> bool:
        """🚀 OPTIMIZED: Enhanced prompt execution logic with context awareness"""
        try:
            # Always execute emergency response if emergency flags exist
            if prompt_type == PromptType.EMERGENCY_RESPONSE:
                return len(trading_context.emergency_flags) > 0 or self.emergency_mode

            # Position-dependent prompts
            if prompt_type in [PromptType.POSITION_MANAGEMENT, PromptType.PROFIT_OPTIMIZATION]:
                return len(trading_context.open_positions) > 0

            # Entry timing only if we have position capacity
            if prompt_type == PromptType.ENTRY_TIMING:
                return self.has_position_capacity() and not self.emergency_mode

            # Check execution intervals to prevent over-execution
            last_execution = self.last_execution_times.get(prompt_type, 0)
            interval = self.prompt_intervals.get(prompt_type, 60)
            time_since_last = time.time() - last_execution

            if time_since_last < interval:
                logger.debug(f"Skipping {prompt_type.value} - executed {time_since_last:.1f}s ago (interval: {interval}s)")
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking prompt execution for {prompt_type}: {e}")
            return False

    def _execute_single_prompt(self, prompt_type: PromptType, trading_context: TradingContext) -> PromptResult:
        """🚀 OPTIMIZED: Execute a single prompt with error handling and performance tracking"""
        start_time = time.time()

        try:
            # Route to appropriate execution method
            if prompt_type == PromptType.EMERGENCY_RESPONSE:
                return self.execute_emergency_response(trading_context)
            elif prompt_type == PromptType.POSITION_MANAGEMENT:
                return self.execute_position_management(trading_context)
            elif prompt_type == PromptType.PROFIT_OPTIMIZATION:
                return self.execute_profit_optimization(trading_context)
            elif prompt_type == PromptType.MARKET_REGIME:
                return self.execute_market_regime_detection(trading_context)
            elif prompt_type == PromptType.RISK_ASSESSMENT:
                return self.execute_risk_assessment(trading_context)
            elif prompt_type == PromptType.ENTRY_TIMING:
                return self.execute_entry_timing(trading_context)
            elif prompt_type == PromptType.STRATEGY_ADAPTATION:
                return self.execute_strategy_adaptation(trading_context)
            elif prompt_type == PromptType.OPPORTUNITY_SCANNER:
                return self.execute_opportunity_scanner(trading_context)
            else:
                raise ValueError(f"Unknown prompt type: {prompt_type}")

        except Exception as e:
            logger.error(f"Error executing {prompt_type.value}: {e}")
            return PromptResult(
                prompt_type=prompt_type,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def _update_context_with_result(self, trading_context: TradingContext, result: PromptResult) -> TradingContext:
        """🚀 OPTIMIZED: Update trading context with previous prompt results for better decision making"""
        try:
            # Create updated context with new information
            updated_context = TradingContext(
                symbol=trading_context.symbol,
                current_price=trading_context.current_price,
                account_balance=trading_context.account_balance,
                open_positions=trading_context.open_positions,
                market_data=trading_context.market_data,
                performance_metrics=trading_context.performance_metrics,
                emergency_flags=trading_context.emergency_flags,
                timestamp=datetime.now()
            )

            # Update market regime if available
            if result.prompt_type == PromptType.MARKET_REGIME and result.success:
                self.market_regime = result.response.get('REGIME', 'UNKNOWN')
                # Update market data with regime information
                updated_context.market_data['regime'] = self.market_regime
                updated_context.market_data['scalp_suitability'] = result.response.get('SCALP_SUITABILITY', 'MEDIUM')

            # Update emergency flags if emergency response executed
            if result.prompt_type == PromptType.EMERGENCY_RESPONSE and result.success:
                action = result.response.get('ACTION', 'MONITOR')
                if action != 'MONITOR':
                    # Clear emergency flags if action was taken
                    updated_context.emergency_flags = []
                    self.emergency_mode = False

            # Update strategy state if strategy adaptation executed
            if result.prompt_type == PromptType.STRATEGY_ADAPTATION and result.success:
                self.strategy_state = result.response.get('STRATEGY_MODE', 'NORMAL')

            return updated_context

        except Exception as e:
            logger.error(f"Error updating context with result: {e}")
            return trading_context  # Return original context on error

    def has_active_positions(self) -> bool:
        """Check if there are active positions"""
        try:
            if hasattr(self.trading_interface, 'real_trading') and self.trading_interface.real_trading:
                positions = self.trading_interface.real_trading.get_open_positions()
                return len(positions) > 0
        except:
            pass
        return False
    
    def has_position_capacity(self) -> bool:
        """Check if we have capacity for new positions"""
        try:
            if hasattr(self.trading_interface, 'real_trading') and self.trading_interface.real_trading:
                positions = self.trading_interface.real_trading.get_open_positions()
                return len(positions) < 3  # Max 3 concurrent positions
        except:
            pass
        return True
    
    def update_emergency_flags(self, trading_context: TradingContext):
        """Update emergency flags based on current conditions"""
        self.emergency_flags.clear()
        
        try:
            # Check for flash crash (>2% price drop in 1 minute)
            if self.detect_flash_crash(trading_context):
                self.emergency_flags.append("FLASH_CRASH")
            
            # Check for liquidity crisis (spread >1%)
            if self.detect_liquidity_crisis(trading_context):
                self.emergency_flags.append("LIQUIDITY_CRISIS")
            
            # Check for margin risk (account risk >80%)
            if self.detect_margin_risk(trading_context):
                self.emergency_flags.append("MARGIN_RISK")
            
            # Check for unusual volume spike
            if self.detect_volume_spike(trading_context):
                self.emergency_flags.append("VOLUME_SPIKE")
            
            # Update emergency mode
            self.emergency_mode = len(self.emergency_flags) > 0
            
        except Exception as e:
            logger.error(f"Error updating emergency flags: {e}")
    
    def detect_flash_crash(self, context: TradingContext) -> bool:
        """Detect flash crash conditions"""
        # Implementation would check recent price movements
        return False
    
    def detect_liquidity_crisis(self, context: TradingContext) -> bool:
        """Detect liquidity crisis conditions"""
        # Implementation would check bid-ask spreads
        return False
    
    def detect_margin_risk(self, context: TradingContext) -> bool:
        """Detect margin risk conditions"""
        # Implementation would check account risk levels
        return False
    
    def detect_volume_spike(self, context: TradingContext) -> bool:
        """Detect unusual volume spikes"""
        # Implementation would check volume patterns
        return False

    def execute_emergency_response(self, context: TradingContext) -> PromptResult:
        """Execute emergency response prompt"""
        start_time = time.time()

        try:
            prompt = self.build_emergency_response_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_emergency_response(response_text)

            execution_time = time.time() - start_time

            # Execute emergency actions if needed
            if response.get('ACTION') in ['CLOSE_ALL_POSITIONS', 'CLOSE_LOSING']:
                self.execute_emergency_actions(response, context)

            return PromptResult(
                prompt_type=PromptType.EMERGENCY_RESPONSE,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in emergency response: {e}")
            return PromptResult(
                prompt_type=PromptType.EMERGENCY_RESPONSE,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_position_management(self, context: TradingContext) -> PromptResult:
        """Execute position management prompt"""
        start_time = time.time()

        try:
            prompt = self.build_position_management_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_position_management_response(response_text)

            execution_time = time.time() - start_time

            # Execute position management actions
            if response.get('ACTION') != 'HOLD':
                self.execute_position_actions(response, context)

            return PromptResult(
                prompt_type=PromptType.POSITION_MANAGEMENT,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in position management: {e}")
            return PromptResult(
                prompt_type=PromptType.POSITION_MANAGEMENT,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_profit_optimization(self, context: TradingContext) -> PromptResult:
        """Execute profit optimization prompt"""
        start_time = time.time()

        try:
            prompt = self.build_profit_optimization_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_profit_optimization_response(response_text)

            execution_time = time.time() - start_time

            # Execute profit optimization actions
            if response.get('ACTION') in ['PARTIAL_CLOSE', 'FULL_CLOSE']:
                self.execute_profit_actions(response, context)

            return PromptResult(
                prompt_type=PromptType.PROFIT_OPTIMIZATION,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in profit optimization: {e}")
            return PromptResult(
                prompt_type=PromptType.PROFIT_OPTIMIZATION,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_market_regime_detection(self, context: TradingContext) -> PromptResult:
        """Execute market regime detection prompt"""
        start_time = time.time()

        try:
            prompt = self.build_market_regime_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_market_regime_response(response_text)

            execution_time = time.time() - start_time

            return PromptResult(
                prompt_type=PromptType.MARKET_REGIME,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in market regime detection: {e}")
            return PromptResult(
                prompt_type=PromptType.MARKET_REGIME,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_risk_assessment(self, context: TradingContext) -> PromptResult:
        """Execute risk assessment prompt"""
        start_time = time.time()

        try:
            prompt = self.build_risk_assessment_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_risk_assessment_response(response_text)

            execution_time = time.time() - start_time

            return PromptResult(
                prompt_type=PromptType.RISK_ASSESSMENT,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in risk assessment: {e}")
            return PromptResult(
                prompt_type=PromptType.RISK_ASSESSMENT,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_entry_timing(self, context: TradingContext) -> PromptResult:
        """Execute entry timing optimization prompt"""
        start_time = time.time()

        try:
            prompt = self.build_entry_timing_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_entry_timing_response(response_text)

            execution_time = time.time() - start_time

            # Execute entry timing actions if immediate entry recommended
            if response.get('ACTION') == 'ENTER_NOW':
                self.execute_entry_actions(response, context)

            return PromptResult(
                prompt_type=PromptType.ENTRY_TIMING,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in entry timing: {e}")
            return PromptResult(
                prompt_type=PromptType.ENTRY_TIMING,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_strategy_adaptation(self, context: TradingContext) -> PromptResult:
        """Execute strategy adaptation prompt"""
        start_time = time.time()

        try:
            prompt = self.build_strategy_adaptation_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_strategy_adaptation_response(response_text)

            execution_time = time.time() - start_time

            # Apply strategy adaptations
            self.apply_strategy_adaptations(response, context)

            return PromptResult(
                prompt_type=PromptType.STRATEGY_ADAPTATION,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in strategy adaptation: {e}")
            return PromptResult(
                prompt_type=PromptType.STRATEGY_ADAPTATION,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_opportunity_scanner(self, context: TradingContext) -> PromptResult:
        """Execute opportunity scanner prompt"""
        start_time = time.time()

        try:
            prompt = self.build_opportunity_scanner_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_opportunity_scanner_response(response_text)

            execution_time = time.time() - start_time

            return PromptResult(
                prompt_type=PromptType.OPPORTUNITY_SCANNER,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in opportunity scanner: {e}")
            return PromptResult(
                prompt_type=PromptType.OPPORTUNITY_SCANNER,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    # Import the builders and parsers
    def __init__(self, lmstudio_runner, main_window):
        from .llm_prompt_builders import LLMPromptBuilders
        from .llm_response_parsers import LLMResponseParsers
        from .llm_action_executors import LLMActionExecutors

        self.lmstudio_runner = lmstudio_runner
        self.main_window = main_window  # Main window has all trading methods
        self.trading_interface = main_window  # For backward compatibility

        # Initialize components
        self.prompt_builders = LLMPromptBuilders(main_window)
        self.response_parsers = LLMResponseParsers()
        self.action_executors = LLMActionExecutors(main_window, main_window)

        # Prompt execution tracking
        self.prompt_results = {}
        self.last_execution_times = {}
        self.prompt_queue = []

        # Emergency and state management
        self.emergency_mode = False
        self.emergency_flags = []
        self.market_regime = "UNKNOWN"
        self.strategy_state = "NORMAL"

        # Performance tracking
        self.prompt_performance = {}
        self.execution_stats = {}

        # Configuration
        self.prompt_intervals = {
            PromptType.EMERGENCY_RESPONSE: 5,      # 5 seconds
            PromptType.POSITION_MANAGEMENT: 10,    # 10 seconds
            PromptType.PROFIT_OPTIMIZATION: 15,    # 15 seconds
            PromptType.MARKET_REGIME: 30,          # 30 seconds
            PromptType.RISK_ASSESSMENT: 45,        # 45 seconds
            PromptType.ENTRY_TIMING: 20,           # 20 seconds
            PromptType.STRATEGY_ADAPTATION: 120,   # 2 minutes
            PromptType.OPPORTUNITY_SCANNER: 60,    # 1 minute
        }

        # Initialize cache manager (lazy import to avoid circular dependency)
        try:
            from utils.cache_manager import CacheManager
            self.cache = CacheManager()
        except ImportError:
            logger.warning("CacheManager not available, using basic caching")
            self.cache = None
        self.w_risk, self.w_timing, self.w_opp = 1.0, 1.0, 1.0
        self.current_support, self.current_resistance = None, None

        logger.info("LLM Prompt Orchestrator initialized")

    def set_main_window(self, main_window):
        """Set main window reference for action executors (already set in __init__)"""
        # Main window is already set in __init__, but keep this method for compatibility
        self.main_window = main_window
        self.action_executors.main_window = main_window

    # Add prompt building methods
    def build_emergency_response_prompt(self, context):
        return self.prompt_builders.build_emergency_response_prompt(context)

    def build_position_management_prompt(self, context):
        return self.prompt_builders.build_position_management_prompt(context)

    def build_profit_optimization_prompt(self, context):
        return self.prompt_builders.build_profit_optimization_prompt(context)

    def build_market_regime_prompt(self, context):
        return self.prompt_builders.build_market_regime_prompt(context)

    def build_risk_assessment_prompt(self, context):
        return self.prompt_builders.build_risk_assessment_prompt(context)

    def build_entry_timing_prompt(self, context):
        return self.prompt_builders.build_entry_timing_prompt(context)

    def build_strategy_adaptation_prompt(self, context):
        return self.prompt_builders.build_strategy_adaptation_prompt(context)

    def build_opportunity_scanner_prompt(self, context):
        return self.prompt_builders.build_opportunity_scanner_prompt(context)

    # Add response parsing methods
    def parse_emergency_response(self, response_text):
        return self.response_parsers.parse_emergency_response(response_text)

    def parse_position_management_response(self, response_text):
        return self.response_parsers.parse_position_management_response(response_text)

    def parse_profit_optimization_response(self, response_text):
        return self.response_parsers.parse_profit_optimization_response(response_text)

    def parse_market_regime_response(self, response_text):
        return self.response_parsers.parse_market_regime_response(response_text)

    def parse_risk_assessment_response(self, response_text):
        return self.response_parsers.parse_risk_assessment_response(response_text)

    def parse_entry_timing_response(self, response_text):
        return self.response_parsers.parse_entry_timing_response(response_text)

    def parse_strategy_adaptation_response(self, response_text):
        return self.response_parsers.parse_strategy_adaptation_response(response_text)

    def parse_opportunity_scanner_response(self, response_text):
        return self.response_parsers.parse_opportunity_scanner_response(response_text)

    # Add action execution methods
    def execute_emergency_actions(self, response, context):
        return self.action_executors.execute_emergency_actions(response, context)

    def execute_position_actions(self, response, context):
        return self.action_executors.execute_position_actions(response, context)

    def execute_profit_actions(self, response, context):
        return self.action_executors.execute_profit_actions(response, context)

    def execute_entry_actions(self, response, context):
        return self.action_executors.execute_entry_actions(response, context)

    def apply_strategy_adaptations(self, response, context):
        return self.action_executors.apply_strategy_adaptations(response, context)

    def run_cycle(self):
        """Test harness: run a single orchestrator cycle as described in the Copilot instructions."""
        # 1) Fetch price & cache it
        price = self.market_api.get_current_price(getattr(self, 'symbol', 'TEST'))
        if self.cache:
            self.cache.add_price(price)
        # 2) Compute & store support/resistance (use test values or defaults)
        support = getattr(self, 'current_support', price * 0.99)
        resistance = getattr(self, 'current_resistance', price * 1.01)
        self.current_support, self.current_resistance = support, resistance
        # 3) Pre-filter
        if not price_in_action_zone(price, support, resistance):
            if self.cache:
                self.cache.add_signal('WAIT', 0.0)
                signal_history_len = len(self.cache.signal_history) if self.cache.signal_history else 0
                return self.schedule_auto_refresh(120 if signal_history_len < 3 else 300)
            else:
                return self.schedule_auto_refresh(120)
        # 4) Build & call prompts with context
        ctx = self.cache.get_context() if self.cache else {}
        r = self.llm_client.call('risk')
        t = self.llm_client.call('timing')
        o = self.llm_client.call('opp')
        # 5) Aggregate, record, execute
        decision, scores = aggregate_signal([r, t, o], [self.w_risk, self.w_timing, self.w_opp])
        confidence = scores[decision]/(self.w_risk+self.w_timing+self.w_opp)
        if self.cache:
            self.cache.add_signal(decision, confidence)
        # 6) Schedule next run
        return self.schedule_auto_refresh(15 if decision != 'WAIT' else 60)

class LmStudioRunner:
    """
    LLM runner for auto-trade integration using LM Studio's OpenAI-compatible API.
    """
    def __init__(self, api_url="http://localhost:1234/v1/chat/completions", model="phi-3.1-mini-128k-instruct"):
        self.api_url = api_url
        self.model = model

    def call(self, prompt: str) -> str:
        headers = {"Content-Type": "application/json"}
        data = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": "You are a trading assistant. Only output the decision, confidence, and rationale."},
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 256,
            "temperature": 0.2,
            "stream": False
        }
        try:
            response = requests.post(self.api_url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            result = response.json()
            # Extract the assistant's reply
            return result['choices'][0]['message']['content']
        except Exception as e:
            return f"DECISION: WAIT\nCONFIDENCE: 0.0\nRATIONALE: LLM API error: {e}"

__all__ = [
    'LLMPromptOrchestrator',
    'aggregate_signal',
    'price_in_action_zone',
    'PromptType',
    'PromptResult',
    'TradingContext',
    'LmStudioRunner',
]
