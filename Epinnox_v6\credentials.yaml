# Multi-account API credentials
# You can define up to 4 different accounts across different exchanges

# Default account (will be used if no account is selected)
default_account: "EPX"

# List of accounts
accounts:
  - name: "EPX"
    exchange: "huobi"
    api_key: "nbtycf4rw2-72d300ec-fb900970-27ef8"
    secret_key: "b4d92e15-523563a0-72a16ad9-9a275"
    description: "EPX - Positions Working"

  - name: "DM"
    exchange: "huobi"
    api_key: "2e0e30b7-vfd5ghr532-16b9f215-cdb2a"
    secret_key: "2712bb50-0f486ad4-********-77f4f"
    description: "DM - Positions Working"

  - name: "Jedi"
    exchange: "huobi"
    api_key: "rbr45t6yr4-c70725b7-18b48f69-32f10"
    secret_key: "8f843fc7-1940a5ef-c5197d8b-a3539"
    description: "Jedi - Positions Not Working"

  - name: "Account 4"
    exchange: "bybit"
    api_key: "your_api_key_4"
    secret_key: "your_secret_key_4"
    description: "Futures Trading Account"

htx:
  apiKey: "nbtycf4rw2-72d300ec-fb900970-27ef8"
  secret: "b4d92e15-523563a0-72a16ad9-9a275"
  password: ""  # HTX passphrase if required
  sandbox: false  # Set to false for live trading

# Legacy format (for backward compatibility)
exchange: 'htx'
apiKey: "nbtycf4rw2-72d300ec-fb900970-27ef8"
secret: "b4d92e15-523563a0-72a16ad9-9a275"
