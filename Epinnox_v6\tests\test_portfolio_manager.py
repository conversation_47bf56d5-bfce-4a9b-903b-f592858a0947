"""
Unit tests for PortfolioManager
"""

import pytest
import asyncio
from datetime import datetime
from portfolio.portfolio_manager import PortfolioManager, Position

class TestPortfolioManager:
    
    @pytest.fixture
    def portfolio(self):
        """Create portfolio manager instance for testing"""
        return PortfolioManager(initial_balance=1000.0, max_positions=3)
    
    @pytest.mark.asyncio
    async def test_can_open_position_success(self, portfolio):
        """Test successful position opening check"""
        result = await portfolio.can_open_position('BTC/USDT', 100.0, 2.0)
        
        assert result['allowed'] is True
        assert result['reason'] == 'All checks passed'
    
    @pytest.mark.asyncio
    async def test_can_open_position_max_positions(self, portfolio):
        """Test position limit enforcement"""
        # Fill up to max positions
        for i in range(3):
            await portfolio.open_position(f'SYMBOL{i}', 'long', 0.1, 100.0, 1.0)
        
        # Try to open one more
        result = await portfolio.can_open_position('BTC/USDT', 100.0, 2.0)
        
        assert result['allowed'] is False
        assert 'Maximum positions' in result['reason']
    
    @pytest.mark.asyncio
    async def test_can_open_position_excessive_size(self, portfolio):
        """Test position size limit enforcement"""
        result = await portfolio.can_open_position('BTC/USDT', 150.0, 1.0)  # 15% of balance
        
        assert result['allowed'] is False
        assert 'exceeds limit' in result['reason']
    
    @pytest.mark.asyncio
    async def test_can_open_position_excessive_leverage(self, portfolio):
        """Test leverage limit enforcement"""
        result = await portfolio.can_open_position('BTC/USDT', 50.0, 15.0)  # 15x leverage
        
        assert result['allowed'] is False
        assert 'exceeds limit' in result['reason']
    
    @pytest.mark.asyncio
    async def test_open_position_success(self, portfolio):
        """Test successful position opening"""
        position = await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0)
        
        assert position.symbol == 'BTC/USDT'
        assert position.side == 'long'
        assert position.size == 0.1
        assert position.entry_price == 50000.0
        assert position.leverage == 2.0
        assert 'BTC/USDT' in portfolio.positions
    
    @pytest.mark.asyncio
    async def test_open_position_switch_sides(self, portfolio):
        """Test switching position sides"""
        # Open long position
        await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0)
        assert portfolio.positions['BTC/USDT'].side == 'long'
        
        # Switch to short (should close long and open short)
        await portfolio.open_position('BTC/USDT', 'short', 0.15, 51000.0, 1.5)
        assert portfolio.positions['BTC/USDT'].side == 'short'
        assert portfolio.positions['BTC/USDT'].size == 0.15
        assert len(portfolio.trade_history) == 1  # One closed trade
    
    @pytest.mark.asyncio
    async def test_close_position_long_profit(self, portfolio):
        """Test closing long position with profit"""
        await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0)
        
        trade_record = await portfolio.close_position('BTC/USDT', 52000.0)
        
        assert trade_record['pnl_usd'] == 400.0  # (52000-50000) * 0.1 * 2
        assert trade_record['pnl_percentage'] == 8.0  # 4% * 2x leverage
        assert portfolio.current_balance == 1400.0
        assert 'BTC/USDT' not in portfolio.positions
    
    @pytest.mark.asyncio
    async def test_close_position_short_profit(self, portfolio):
        """Test closing short position with profit"""
        await portfolio.open_position('ETH/USDT', 'short', 1.0, 3000.0, 1.0)
        
        trade_record = await portfolio.close_position('ETH/USDT', 2900.0)
        
        assert trade_record['pnl_usd'] == 100.0  # (3000-2900) * 1.0 * 1
        assert portfolio.current_balance == 1100.0
    
    @pytest.mark.asyncio
    async def test_close_position_loss(self, portfolio):
        """Test closing position with loss"""
        await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0)
        
        trade_record = await portfolio.close_position('BTC/USDT', 48000.0)
        
        assert trade_record['pnl_usd'] == -400.0  # (48000-50000) * 0.1 * 2
        assert portfolio.current_balance == 600.0
    
    @pytest.mark.asyncio
    async def test_close_nonexistent_position(self, portfolio):
        """Test closing non-existent position"""
        with pytest.raises(Exception) as exc_info:
            await portfolio.close_position('NONEXISTENT', 50000.0)
        
        assert 'No position found' in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_update_positions(self, portfolio):
        """Test position price updates"""
        await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0)
        await portfolio.open_position('ETH/USDT', 'short', 1.0, 3000.0, 1.0)
        
        price_updates = {
            'BTC/USDT': 51000.0,
            'ETH/USDT': 2950.0
        }
        
        await portfolio.update_positions(price_updates)
        
        btc_position = portfolio.positions['BTC/USDT']
        eth_position = portfolio.positions['ETH/USDT']
        
        assert btc_position.current_price == 51000.0
        assert btc_position.unrealized_pnl == 200.0  # (51000-50000) * 0.1 * 2
        assert eth_position.current_price == 2950.0
        assert eth_position.unrealized_pnl == 50.0   # (3000-2950) * 1.0 * 1
    
    @pytest.mark.asyncio
    async def test_check_risk_levels_stop_loss(self, portfolio):
        """Test stop loss triggering"""
        await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0, stop_loss=49000.0)
        
        # Update price to trigger stop loss
        await portfolio.check_risk_levels('BTC/USDT', 48500.0)
        
        # Position should be closed
        assert 'BTC/USDT' not in portfolio.positions
        assert len(portfolio.trade_history) == 1
    
    @pytest.mark.asyncio
    async def test_check_risk_levels_take_profit(self, portfolio):
        """Test take profit triggering"""
        await portfolio.open_position('BTC/USDT', 'long', 0.1, 50000.0, 2.0, take_profit=52000.0)
        
        # Update price to trigger take profit
        await portfolio.check_risk_levels('BTC/USDT', 52500.0)
        
        # Position should be closed
        assert 'BTC/USDT' not in portfolio.positions
        assert len(portfolio.trade_history) == 1
    
    def test_calculate_total_portfolio_risk(self, portfolio):
        """Test portfolio risk calculation"""
        # Manually add positions for testing
        portfolio.positions['BTC/USDT'] = Position(
            symbol='BTC/USDT',
            side='long',
            size=0.1,
            entry_price=50000.0,
            current_price=51000.0,
            leverage=2.0,
            unrealized_pnl=0.0,
            realized_pnl=0.0,
            timestamp=datetime.now()
        )
        
        portfolio.positions['ETH/USDT'] = Position(
            symbol='ETH/USDT',
            side='short',
            size=1.0,
            entry_price=3000.0,
            current_price=2950.0,
            leverage=1.5,
            unrealized_pnl=0.0,
            realized_pnl=0.0,
            timestamp=datetime.now()
        )
        
        risk = portfolio.calculate_total_portfolio_risk()
        
        # BTC: (0.1 * 51000 * 2) / 1000 = 10.2 -> 0.102 (10.2%)
        # ETH: (1.0 * 2950 * 1.5) / 1000 = 4.425 -> 0.04425 (4.425%)
        # Total: ~0.14625 (14.625%)
        assert abs(risk - 0.14625) < 0.001
    
    def test_get_portfolio_summary(self, portfolio):
        """Test portfolio summary generation"""
        # Add a position
        portfolio.positions['BTC/USDT'] = Position(
            symbol='BTC/USDT',
            side='long',
            size=0.1,
            entry_price=50000.0,
            current_price=51000.0,
            leverage=2.0,
            unrealized_pnl=200.0,
            realized_pnl=0.0,
            timestamp=datetime.now()
        )
        
        summary = portfolio.get_portfolio_summary()
        
        assert summary['balance'] == 1000.0
        assert summary['total_value'] == 1200.0  # 1000 + 200 unrealized
        assert summary['unrealized_pnl'] == 200.0
        assert summary['open_positions'] == 1
        assert 'BTC/USDT' in summary['positions']
    
    def test_reset_daily_pnl(self, portfolio):
        """Test daily PnL reset"""
        portfolio.daily_pnl = 150.0
        portfolio.reset_daily_pnl()
        
        assert portfolio.daily_pnl == 0.0
    
    def test_set_risk_limits(self, portfolio):
        """Test risk limit updates"""
        portfolio.set_risk_limits(
            max_portfolio_risk=0.25,
            max_position_size=0.12,
            max_leverage=8.0,
            max_daily_loss=0.04
        )
        
        assert portfolio.max_portfolio_risk == 0.25
        assert portfolio.max_position_size == 0.12
        assert portfolio.max_leverage == 8.0
        assert portfolio.max_daily_loss == 0.04
