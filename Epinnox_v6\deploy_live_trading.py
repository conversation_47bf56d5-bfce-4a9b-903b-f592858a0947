#!/usr/bin/env python3
"""
Live Trading Deployment Script
Deploy Epinnox autonomous trading system for live production trading
"""

import asyncio
import logging
import signal
import sys
import os
import time
from datetime import datetime
from pathlib import Path

# Configure logging for live trading
log_filename = f'live_trading_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LiveTradingDeployment:
    """
    Live trading deployment with comprehensive safety measures
    """
    
    def __init__(self):
        self.config_path = "config/live_production.yaml"
        self.deployment = None
        self.running = False
        self.start_time = None
        
    async def deploy(self):
        """Deploy live trading system"""
        try:
            logger.info("🚀 EPINNOX LIVE TRADING DEPLOYMENT")
            logger.info("="*60)
            
            # Step 1: Load credentials
            if not self._load_credentials():
                return False
            
            # Step 2: Run pre-deployment validation
            if not await self._run_pre_deployment_validation():
                return False
            
            # Step 3: Final safety confirmation
            if not self._final_safety_confirmation():
                return False
            
            # Step 4: Initialize deployment
            if not await self._initialize_deployment():
                return False
            
            # Step 5: Start live trading
            await self._start_live_trading()
            
            return True
            
        except KeyboardInterrupt:
            logger.info("🛑 Deployment interrupted by user")
            await self._emergency_shutdown()
        except Exception as e:
            logger.error(f"❌ Deployment failed: {e}")
            await self._emergency_shutdown()
            return False
    
    def _load_credentials(self):
        """Load API credentials"""
        try:
            logger.info("🔐 Loading API credentials...")

            # Try to load from credentials.yaml file first
            credentials_file = Path("credentials.yaml")
            if credentials_file.exists():
                logger.info("📂 Loading credentials from credentials.yaml...")
                with open(credentials_file, 'r') as f:
                    import yaml
                    creds = yaml.safe_load(f)

                # Extract HTX credentials
                htx_creds = creds.get('htx', {})
                api_key = htx_creds.get('apiKey')
                secret_key = htx_creds.get('secret')
                passphrase = htx_creds.get('password', '')

                if api_key and secret_key:
                    # Set environment variables for the system to use
                    os.environ['HTX_API_KEY'] = api_key
                    os.environ['HTX_SECRET_KEY'] = secret_key
                    if passphrase:
                        os.environ['HTX_PASSPHRASE'] = passphrase

                    logger.info("✅ Credentials loaded from credentials.yaml")
                    return True

            # Fallback to .env file
            env_file = Path(".env")
            if env_file.exists():
                logger.info("📂 Loading credentials from .env file...")
                with open(env_file, "r") as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith("#") and "=" in line:
                            key, value = line.split("=", 1)
                            os.environ[key] = value

                # Verify credentials are available
                if os.getenv('HTX_API_KEY') and os.getenv('HTX_SECRET_KEY'):
                    logger.info("✅ Credentials loaded from .env file")
                    return True

            # Check environment variables
            if os.getenv('HTX_API_KEY') and os.getenv('HTX_SECRET_KEY'):
                logger.info("✅ Credentials loaded from environment variables")
                return True

            logger.error("❌ No API credentials found")
            logger.error("Please ensure credentials are in credentials.yaml, .env file, or environment variables")
            return False

        except Exception as e:
            logger.error(f"❌ Failed to load credentials: {e}")
            return False
    
    async def _run_pre_deployment_validation(self):
        """Run comprehensive pre-deployment validation"""
        try:
            logger.info("🔍 Running pre-deployment validation...")
            
            # Import and run validation
            from pre_deployment_validation import LiveTradingValidator
            
            validator = LiveTradingValidator(self.config_path)
            success = await validator.run_validation()
            
            if not success:
                logger.error("❌ Pre-deployment validation failed")
                logger.error("Please fix the issues before proceeding")
                return False
            
            logger.info("✅ Pre-deployment validation passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Validation error: {e}")
            return False
    
    def _final_safety_confirmation(self):
        """Final safety confirmation before live trading"""
        logger.warning("\n" + "🚨"*20)
        logger.warning("FINAL SAFETY CONFIRMATION")
        logger.warning("🚨"*20)
        logger.warning("\nYou are about to start LIVE TRADING with REAL MONEY")
        logger.warning("Account Balance: ~$50 USD")
        logger.warning("Risk Settings:")
        logger.warning("• Max Daily Loss: $10 (20% of account)")
        logger.warning("• Max Position Size: $15 (30% of account)")
        logger.warning("• Max Leverage: 3x")
        logger.warning("• Max Positions: 2")
        logger.warning("\nSafety Features Active:")
        logger.warning("• Emergency stop procedures")
        logger.warning("• Real-time risk monitoring")
        logger.warning("• Automatic position closure")
        logger.warning("• Balance protection")
        
        print("\n" + "⚠️ "*10)
        response = input("Type 'START LIVE TRADING' to confirm (anything else cancels): ")
        
        if response.strip() != 'START LIVE TRADING':
            logger.info("❌ Live trading cancelled by user")
            return False
        
        logger.warning("✅ Live trading confirmed - Starting in 5 seconds...")
        time.sleep(5)
        return True
    
    async def _initialize_deployment(self):
        """Initialize the deployment system"""
        try:
            logger.info("🔧 Initializing deployment system...")
            
            from deploy_autonomous_trading import AutonomousTradingDeployment
            
            self.deployment = AutonomousTradingDeployment(
                config_path=self.config_path,
                mode='live'
            )
            
            # Initialize system
            success = await self.deployment.initialize_system()
            if not success:
                logger.error("❌ System initialization failed")
                return False
            
            logger.info("✅ Deployment system initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Deployment initialization failed: {e}")
            return False
    
    async def _start_live_trading(self):
        """Start live trading with monitoring"""
        try:
            self.start_time = datetime.now()
            self.running = True
            
            logger.info("🚀 STARTING LIVE AUTONOMOUS TRADING")
            logger.info("="*60)
            logger.info(f"Start Time: {self.start_time}")
            logger.info(f"Log File: {log_filename}")
            logger.info("="*60)
            
            # Set up signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # Start monitoring task
            monitor_task = asyncio.create_task(self._monitoring_loop())
            
            # Start trading task
            trading_task = asyncio.create_task(self.deployment.start_autonomous_trading())
            
            # Wait for either task to complete
            done, pending = await asyncio.wait(
                [monitor_task, trading_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Cancel remaining tasks
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
        except Exception as e:
            logger.error(f"❌ Live trading error: {e}")
        finally:
            await self._emergency_shutdown()
    
    async def _monitoring_loop(self):
        """Continuous monitoring loop"""
        while self.running:
            try:
                # Log system status every minute
                if self.deployment and self.deployment.orchestrator:
                    status = self.deployment.orchestrator.get_status()
                    
                    runtime = datetime.now() - self.start_time
                    logger.info(f"📊 LIVE TRADING STATUS - Runtime: {runtime}")
                    logger.info(f"   State: {status['state']}")
                    logger.info(f"   Cycles: {status['cycle_count']}")
                    logger.info(f"   Emergency Stop: {status['emergency_stop']}")
                    
                    # Check for emergency conditions
                    if status['emergency_stop']:
                        logger.critical("🚨 EMERGENCY STOP DETECTED - SHUTTING DOWN")
                        self.running = False
                        break
                
                await asyncio.sleep(60)  # Monitor every minute
                
            except Exception as e:
                logger.error(f"❌ Monitoring error: {e}")
                await asyncio.sleep(10)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"🛑 Received signal {signum} - Initiating graceful shutdown...")
        self.running = False
    
    async def _emergency_shutdown(self):
        """Emergency shutdown procedure"""
        try:
            logger.critical("🚨 INITIATING EMERGENCY SHUTDOWN")
            
            self.running = False
            
            if self.deployment:
                await self.deployment._shutdown()
            
            runtime = datetime.now() - self.start_time if self.start_time else "Unknown"
            logger.info(f"🛑 Live trading stopped - Runtime: {runtime}")
            logger.info(f"📄 Log file saved: {log_filename}")
            
        except Exception as e:
            logger.error(f"❌ Emergency shutdown error: {e}")

async def main():
    """Main deployment function"""
    # Check if configuration exists
    config_path = Path("config/live_production.yaml")
    if not config_path.exists():
        logger.error(f"❌ Configuration file not found: {config_path}")
        logger.error("Please ensure the live production configuration is available")
        sys.exit(1)
    
    # Create deployment instance
    deployment = LiveTradingDeployment()
    
    # Run deployment
    success = await deployment.deploy()
    
    if not success:
        logger.error("❌ Live trading deployment failed")
        sys.exit(1)

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 Deployment interrupted")
    except Exception as e:
        logger.error(f"❌ Deployment error: {e}")
        sys.exit(1)
